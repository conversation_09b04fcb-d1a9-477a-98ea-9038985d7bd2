/**
 * 数据库时间迁移脚本
 * 将现有的UTC时间数据转换为北京时间
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const dayjs = require('dayjs');
const utc = require('dayjs/plugin/utc');
const timezone = require('dayjs/plugin/timezone');

// 加载dayjs插件
dayjs.extend(utc);
dayjs.extend(timezone);

const BEIJING_TIMEZONE = 'Asia/Shanghai';
const DB_PATH = path.join(__dirname, 'database.sqlite');

/**
 * 将UTC时间转换为北京时间字符串
 */
function convertUTCToBeijing(utcTimeString) {
  if (!utcTimeString) return null;
  
  try {
    // 解析UTC时间并转换为北京时间
    const beijingTime = dayjs(utcTimeString).utc().tz(BEIJING_TIMEZONE);
    return beijingTime.format('YYYY-MM-DD HH:mm:ss');
  } catch (error) {
    console.error('时间转换失败:', utcTimeString, error);
    return utcTimeString; // 转换失败时返回原值
  }
}

/**
 * 迁移用户表时间字段
 */
async function migrateUsersTable(db) {
  return new Promise((resolve, reject) => {
    console.log('🔄 开始迁移用户表时间字段...');
    
    // 获取所有用户数据
    db.all('SELECT id, created_at, updated_at FROM users', [], (err, rows) => {
      if (err) {
        reject(err);
        return;
      }
      
      if (rows.length === 0) {
        console.log('✅ 用户表无数据需要迁移');
        resolve();
        return;
      }
      
      let completed = 0;
      const total = rows.length;
      
      rows.forEach(row => {
        const beijingCreatedAt = convertUTCToBeijing(row.created_at);
        const beijingUpdatedAt = convertUTCToBeijing(row.updated_at);
        
        db.run(
          'UPDATE users SET created_at = ?, updated_at = ? WHERE id = ?',
          [beijingCreatedAt, beijingUpdatedAt, row.id],
          (err) => {
            if (err) {
              console.error(`❌ 更新用户 ${row.id} 失败:`, err);
            } else {
              console.log(`✅ 用户 ${row.id}: ${row.created_at} -> ${beijingCreatedAt}`);
            }
            
            completed++;
            if (completed === total) {
              console.log(`✅ 用户表迁移完成，共处理 ${total} 条记录`);
              resolve();
            }
          }
        );
      });
    });
  });
}

/**
 * 迁移合同表时间字段
 */
async function migrateContractsTable(db) {
  return new Promise((resolve, reject) => {
    console.log('🔄 开始迁移合同表时间字段...');
    
    // 获取所有合同数据
    db.all('SELECT id, created_at, updated_at, submitted_at, reviewed_at FROM contracts', [], (err, rows) => {
      if (err) {
        reject(err);
        return;
      }
      
      if (rows.length === 0) {
        console.log('✅ 合同表无数据需要迁移');
        resolve();
        return;
      }
      
      let completed = 0;
      const total = rows.length;
      
      rows.forEach(row => {
        const beijingCreatedAt = convertUTCToBeijing(row.created_at);
        const beijingUpdatedAt = convertUTCToBeijing(row.updated_at);
        const beijingSubmittedAt = convertUTCToBeijing(row.submitted_at);
        const beijingReviewedAt = convertUTCToBeijing(row.reviewed_at);
        
        db.run(
          'UPDATE contracts SET created_at = ?, updated_at = ?, submitted_at = ?, reviewed_at = ? WHERE id = ?',
          [beijingCreatedAt, beijingUpdatedAt, beijingSubmittedAt, beijingReviewedAt, row.id],
          (err) => {
            if (err) {
              console.error(`❌ 更新合同 ${row.id} 失败:`, err);
            } else {
              console.log(`✅ 合同 ${row.id}: ${row.created_at} -> ${beijingCreatedAt}`);
            }
            
            completed++;
            if (completed === total) {
              console.log(`✅ 合同表迁移完成，共处理 ${total} 条记录`);
              resolve();
            }
          }
        );
      });
    });
  });
}

/**
 * 迁移通知表时间字段
 */
async function migrateNotificationsTable(db) {
  return new Promise((resolve, reject) => {
    console.log('🔄 开始迁移通知表时间字段...');
    
    // 获取所有通知数据
    db.all('SELECT id, created_at, read_at FROM notifications', [], (err, rows) => {
      if (err) {
        reject(err);
        return;
      }
      
      if (rows.length === 0) {
        console.log('✅ 通知表无数据需要迁移');
        resolve();
        return;
      }
      
      let completed = 0;
      const total = rows.length;
      
      rows.forEach(row => {
        const beijingCreatedAt = convertUTCToBeijing(row.created_at);
        const beijingReadAt = convertUTCToBeijing(row.read_at);
        
        db.run(
          'UPDATE notifications SET created_at = ?, read_at = ? WHERE id = ?',
          [beijingCreatedAt, beijingReadAt, row.id],
          (err) => {
            if (err) {
              console.error(`❌ 更新通知 ${row.id} 失败:`, err);
            } else {
              console.log(`✅ 通知 ${row.id}: ${row.created_at} -> ${beijingCreatedAt}`);
            }
            
            completed++;
            if (completed === total) {
              console.log(`✅ 通知表迁移完成，共处理 ${total} 条记录`);
              resolve();
            }
          }
        );
      });
    });
  });
}

/**
 * 迁移审核历史表时间字段
 */
async function migrateContractReviewsTable(db) {
  return new Promise((resolve, reject) => {
    console.log('🔄 开始迁移审核历史表时间字段...');
    
    // 获取所有审核历史数据
    db.all('SELECT id, created_at FROM contract_reviews', [], (err, rows) => {
      if (err) {
        reject(err);
        return;
      }
      
      if (rows.length === 0) {
        console.log('✅ 审核历史表无数据需要迁移');
        resolve();
        return;
      }
      
      let completed = 0;
      const total = rows.length;
      
      rows.forEach(row => {
        const beijingCreatedAt = convertUTCToBeijing(row.created_at);
        
        db.run(
          'UPDATE contract_reviews SET created_at = ? WHERE id = ?',
          [beijingCreatedAt, row.id],
          (err) => {
            if (err) {
              console.error(`❌ 更新审核历史 ${row.id} 失败:`, err);
            } else {
              console.log(`✅ 审核历史 ${row.id}: ${row.created_at} -> ${beijingCreatedAt}`);
            }
            
            completed++;
            if (completed === total) {
              console.log(`✅ 审核历史表迁移完成，共处理 ${total} 条记录`);
              resolve();
            }
          }
        );
      });
    });
  });
}

/**
 * 主迁移函数
 */
async function migrateDatabase() {
  const db = new sqlite3.Database(DB_PATH);
  
  try {
    console.log('🚀 开始数据库时间迁移...');
    console.log(`📁 数据库路径: ${DB_PATH}`);
    
    // 依次迁移各个表
    await migrateUsersTable(db);
    await migrateContractsTable(db);
    await migrateNotificationsTable(db);
    await migrateContractReviewsTable(db);
    
    console.log('🎉 数据库时间迁移完成！');
    console.log('📝 所有时间字段已从UTC时间转换为北京时间');
    
  } catch (error) {
    console.error('❌ 数据库迁移失败:', error);
    process.exit(1);
  } finally {
    db.close((err) => {
      if (err) {
        console.error('❌ 关闭数据库连接失败:', err);
      } else {
        console.log('✅ 数据库连接已关闭');
      }
    });
  }
}

// 执行迁移
if (require.main === module) {
  migrateDatabase();
}

module.exports = { migrateDatabase, convertUTCToBeijing };
