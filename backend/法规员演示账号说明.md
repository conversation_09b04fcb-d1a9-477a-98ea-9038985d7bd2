# 法规员演示账号说明

## 账号信息

### 新创建的演示账号
- **用户名**: `fagui_demo`
- **密码**: `123456`
- **角色**: 市局法规员
- **状态**: 激活

### 原有的法规员账号
- **用户名**: `legal_officer`
- **密码**: `123456`
- **角色**: 市局法规员
- **状态**: 激活

## 法规员功能权限

法规员在合同管理系统中具有以下权限：

### 1. 合同查看权限
- 可以查看所有合同信息
- 可以查看合同审核历史
- 可以查看合同详细信息

### 2. 合同编号管理权限
- **查看待分配编号的合同**: 可以查看状态为 `pending_contract_number` 的合同
- **分配合同编号**: 为通过审核的合同分配正式的合同编号
- **管理合同编号**: 可以修改或重新分配合同编号

### 3. 通知权限
- 可以接收系统通知
- 可以查看相关的工作提醒

## 工作流程

法规员在合同审核流程中的位置：

```
员工提交合同 → 县局审核 → 市局审核 → 法规员分配编号 → 合同完成
```

### 具体步骤：
1. **合同审核完成**: 当合同通过县局和市局的审核后，状态变为 `pending_contract_number`
2. **法规员处理**: 法规员登录系统，查看待分配编号的合同列表
3. **分配编号**: 为合同分配正式的合同编号（格式：HT + 3位数字，如 HT001）
4. **完成流程**: 分配编号后，合同状态变为 `completed`

## 系统登录

### 前端登录地址
- 开发环境: `http://localhost:3000`
- 使用上述账号信息登录

### 后端API
- 开发环境: `http://localhost:5000`
- 法规员相关API端点：
  - `GET /api/contracts/pending-number` - 获取待分配编号的合同
  - `POST /api/contracts/:id/assign-number` - 分配合同编号

## 测试建议

为了完整测试法规员功能，建议按以下步骤操作：

1. **准备测试数据**:
   - 使用员工账号创建合同
   - 使用县局审核员账号审核通过
   - 使用市局审核员账号审核通过

2. **法规员操作**:
   - 使用法规员账号登录
   - 查看待分配编号的合同列表
   - 为合同分配编号
   - 确认合同状态变为已完成

3. **验证结果**:
   - 检查合同编号是否正确分配
   - 确认合同状态流转正确
   - 验证相关通知是否发送

## 注意事项

1. **合同编号格式**: 系统使用 `HT + 3位数字` 的格式（如：HT001, HT002）
2. **编号唯一性**: 每个合同编号在系统中必须唯一
3. **权限限制**: 法规员只能处理已通过审核的合同
4. **操作记录**: 所有编号分配操作都会记录在系统日志中

## 相关脚本

- **创建法规员账号**: `node create-legal-officer-demo.js`
- **测试法规员工作流**: `node test-legal-officer-workflow.js`
- **验证RBAC权限**: `node verify-rbac.js`
