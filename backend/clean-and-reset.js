#!/usr/bin/env node

/**
 * 清理和重置合同审核系统数据库脚本
 * 删除所有测试数据并重新生成完整的RBAC权限系统
 */

const sqlite3 = require('sqlite3').verbose();
const bcrypt = require('bcrypt');
const path = require('path');
const fs = require('fs');
const DATABASE_CONFIG = require('./src/config/database');
const DateTimeUtils = require('./src/utils/dateTime');

// 使用统一的数据库配置
const DB_PATH = DATABASE_CONFIG.path;
const UPLOAD_DIR = path.join(__dirname, 'uploads');
const AVATARS_DIR = path.join(UPLOAD_DIR, 'avatars');

console.log('🧹 开始清理和重置合同审核系统（包含RBAC权限系统）...');

// 创建数据库连接
const db = new sqlite3.Database(DB_PATH, (err) => {
  if (err) {
    console.error('❌ 数据库连接失败:', err.message);
    process.exit(1);
  }
  console.log('✅ 数据库连接成功:', DB_PATH);
});

// Promise化数据库操作
function runQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function (err) {
      if (err) {
        reject(err);
      } else {
        resolve({ changes: this.changes, lastID: this.lastID });
      }
    });
  });
}

function getQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
}

function allQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
}

// 清理文件夹中的所有文件
function cleanDirectory(dirPath, description) {
  if (!fs.existsSync(dirPath)) {
    console.log(`📁 目录不存在，跳过清理: ${dirPath}`);
    return;
  }

  const files = fs.readdirSync(dirPath);
  let deletedCount = 0;

  files.forEach(file => {
    const filePath = path.join(dirPath, file);
    const stat = fs.statSync(filePath);

    if (stat.isFile()) {
      try {
        fs.unlinkSync(filePath);
        deletedCount++;
      } catch (error) {
        console.error(`❌ 删除文件失败: ${filePath}`, error.message);
      }
    }
  });

  console.log(`🗑️ 清理${description}: 删除了 ${deletedCount} 个文件`);
}

// 主清理和重置函数
async function cleanAndReset() {
  try {
    console.log('\n📋 第一步：清理上传文件...');

    // 清理PDF文件
    cleanDirectory(UPLOAD_DIR, 'PDF文件');

    // 清理头像文件
    cleanDirectory(AVATARS_DIR, '头像文件');

    console.log('\n📋 第二步：清理数据库数据...');

    // 启用外键约束
    await runQuery('PRAGMA foreign_keys = ON');

    // 清理合同相关数据
    const contractsResult = await runQuery('DELETE FROM contracts');
    console.log(`🗑️ 清理合同数据: 删除了 ${contractsResult.changes} 条记录`);

    // 清理通知数据
    const notificationsResult = await runQuery('DELETE FROM notifications');
    console.log(`🗑️ 清理通知数据: 删除了 ${notificationsResult.changes} 条记录`);

    // 清理操作日志
    const logsResult = await runQuery('DELETE FROM operation_logs');
    console.log(`🗑️ 清理操作日志: 删除了 ${logsResult.changes} 条记录`);

    // 清理所有用户数据
    const usersResult = await runQuery('DELETE FROM users');
    console.log(`🗑️ 清理用户数据: 删除了 ${usersResult.changes} 条记录`);

    // 清理RBAC相关数据
    try {
      const rolePermissionsResult = await runQuery('DELETE FROM role_permissions');
      console.log(`🗑️ 清理角色权限关联: 删除了 ${rolePermissionsResult.changes} 条记录`);

      const permissionsResult = await runQuery('DELETE FROM permissions');
      console.log(`🗑️ 清理权限数据: 删除了 ${permissionsResult.changes} 条记录`);

      const rolesResult = await runQuery('DELETE FROM roles');
      console.log(`🗑️ 清理角色数据: 删除了 ${rolesResult.changes} 条记录`);

      const migrationsResult = await runQuery('DELETE FROM migrations');
      console.log(`🗑️ 清理迁移记录: 删除了 ${migrationsResult.changes} 条记录`);
    } catch (error) {
      console.log('ℹ️  RBAC表可能不存在，跳过清理');
    }

    // 重置自增ID
    await runQuery('DELETE FROM sqlite_sequence');
    console.log('🔄 重置所有自增ID计数器');

    console.log('\n📋 第三步：初始化RBAC权限系统...');

    // 初始化RBAC系统
    await initializeRBACSystem();

    console.log('\n📋 第四步：创建用户账号...');

    // 生成加密密码
    const saltRounds = process.env.NODE_ENV === 'production' ? 6 : 4;
    const adminPassword = await bcrypt.hash('admin123', saltRounds);
    const defaultPassword = await bcrypt.hash('123456', saltRounds);

    // 获取角色ID
    const adminRole = await getQuery('SELECT id FROM roles WHERE name = ?', ['admin']);
    const employeeRole = await getQuery('SELECT id FROM roles WHERE name = ?', ['employee']);
    const countyReviewerRole = await getQuery('SELECT id FROM roles WHERE name = ?', ['county_reviewer']);
    const cityReviewerRole = await getQuery('SELECT id FROM roles WHERE name = ?', ['city_reviewer']);

    // 创建用户结构
    const users = [
      { username: 'admin', password: adminPassword, role: 'admin', role_id: adminRole.id, description: '系统管理员' },
      { username: 'employee', password: defaultPassword, role: 'employee', role_id: employeeRole.id, description: '员工' },
      { username: 'county_reviewer', password: defaultPassword, role: 'county_reviewer', role_id: countyReviewerRole.id, description: '县级审核员' },
      { username: 'city_reviewer', password: defaultPassword, role: 'city_reviewer', role_id: cityReviewerRole.id, description: '市级审核员' }
    ];

    for (const user of users) {
      const result = await runQuery(`
        INSERT INTO users (username, password, role, role_id, status, created_by, created_at, updated_at)
        VALUES (?, ?, ?, ?, 'active', ?, ?, ?)
      `, [
        user.username,
        user.password,
        user.role,
        user.role_id,
        user.username === 'admin' ? null : 1,
        DateTimeUtils.nowUTC(),
        DateTimeUtils.nowUTC()
      ]);

      console.log(`✅ 创建用户: ${user.username} (${user.description}) - ID: ${result.lastID}`);
    }

    console.log('\n🎉 清理和重置完成！');
    console.log('\n📝 新的用户账号信息（包含RBAC权限系统）：');
    console.log('  🔑 管理员: admin / admin123 (拥有所有权限)');
    console.log('  👤 员工: employee / 123456 (合同录入权限)');
    console.log('  🏛️  县级审核员: county_reviewer / 123456 (县级审核权限)');
    console.log('  🏢 市级审核员: city_reviewer / 123456 (市级审核权限)');
    console.log('\n🔐 RBAC权限系统已完整配置：');
    console.log('  ✅ 4个角色已创建');
    console.log('  ✅ 18个权限已配置');
    console.log('  ✅ 角色权限关联已建立');
    console.log('  ✅ 所有时间戳使用北京时间（UTC+8）');

  } catch (error) {
    console.error('❌ 清理和重置失败:', error.message);
    process.exit(1);
  } finally {
    db.close((err) => {
      if (err) {
        console.error('❌ 关闭数据库连接失败:', err.message);
      } else {
        console.log('✅ 数据库连接已关闭');
      }
    });
  }
}

// RBAC系统初始化函数
async function initializeRBACSystem() {
  console.log('🔐 开始初始化RBAC权限系统...');

  // 1. 创建RBAC表结构（如果不存在）
  await createRBACTables();

  // 2. 插入默认角色
  await insertDefaultRoles();

  // 3. 插入默认权限
  await insertDefaultPermissions();

  // 4. 设置角色权限关联
  await setupRolePermissions();

  console.log('✅ RBAC权限系统初始化完成');
}

// 创建RBAC表结构
async function createRBACTables() {
  try {
    // 创建角色表
    await runQuery(`
      CREATE TABLE IF NOT EXISTS roles (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name VARCHAR(50) UNIQUE NOT NULL,
        display_name VARCHAR(100) NOT NULL,
        description TEXT,
        level INTEGER DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ 创建角色表');

    // 创建权限表
    await runQuery(`
      CREATE TABLE IF NOT EXISTS permissions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name VARCHAR(100) UNIQUE NOT NULL,
        action VARCHAR(50) NOT NULL,
        subject VARCHAR(50) NOT NULL,
        description TEXT,
        category VARCHAR(50) DEFAULT 'general',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ 创建权限表');

    // 创建角色权限关联表
    await runQuery(`
      CREATE TABLE IF NOT EXISTS role_permissions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        role_id INTEGER NOT NULL,
        permission_id INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
        FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
        UNIQUE(role_id, permission_id)
      )
    `);
    console.log('✅ 创建角色权限关联表');

    // 创建迁移记录表
    await runQuery(`
      CREATE TABLE IF NOT EXISTS migrations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        migration_name VARCHAR(255) NOT NULL,
        version VARCHAR(50) NOT NULL,
        executed_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ 创建迁移记录表');

    // 为users表添加role_id列（如果不存在）
    try {
      await runQuery('ALTER TABLE users ADD COLUMN role_id INTEGER REFERENCES roles(id)');
      console.log('✅ 为users表添加role_id列');
    } catch (error) {
      console.log('ℹ️  users表的role_id列已存在');
    }

  } catch (error) {
    console.error('❌ 创建RBAC表失败:', error.message);
    throw error;
  }
}

// 插入默认角色数据
async function insertDefaultRoles() {
  const roles = [
    {
      name: 'employee',
      display_name: '员工',
      description: '普通员工，负责合同录入和基本操作',
      level: 1
    },
    {
      name: 'county_reviewer',
      display_name: '县级审核员',
      description: '县级审核员，负责县级合同审核',
      level: 2
    },
    {
      name: 'city_reviewer',
      display_name: '市级审核员',
      description: '市级审核员，负责市级合同审核',
      level: 3
    },
    {
      name: 'admin',
      display_name: '超级管理员',
      description: '系统管理员，拥有所有权限',
      level: 4
    }
  ];

  for (const role of roles) {
    try {
      const result = await runQuery(`
        INSERT INTO roles (name, display_name, description, level, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [role.name, role.display_name, role.description, role.level, DateTimeUtils.nowUTC(), DateTimeUtils.nowUTC()]);
      console.log(`✅ 插入角色: ${role.display_name} (ID: ${result.lastID})`);
    } catch (error) {
      console.error(`❌ 插入角色失败 ${role.name}:`, error.message);
    }
  }
}

// 插入默认权限数据
async function insertDefaultPermissions() {
  const permissions = [
    // 合同权限
    { name: 'contract:create', action: 'create', subject: 'contract', description: '创建合同', category: 'contract' },
    { name: 'contract:read', action: 'read', subject: 'contract', description: '查看合同', category: 'contract' },
    { name: 'contract:update', action: 'update', subject: 'contract', description: '更新合同', category: 'contract' },
    { name: 'contract:delete', action: 'delete', subject: 'contract', description: '删除合同', category: 'contract' },
    { name: 'contract:review', action: 'review', subject: 'contract', description: '审核合同', category: 'contract' },
    { name: 'contract:approve', action: 'approve', subject: 'contract', description: '批准合同', category: 'contract' },
    { name: 'contract:reject', action: 'reject', subject: 'contract', description: '拒绝合同', category: 'contract' },

    // 用户权限
    { name: 'user:create', action: 'create', subject: 'user', description: '创建用户', category: 'user' },
    { name: 'user:read', action: 'read', subject: 'user', description: '查看用户', category: 'user' },
    { name: 'user:update', action: 'update', subject: 'user', description: '更新用户', category: 'user' },
    { name: 'user:delete', action: 'delete', subject: 'user', description: '删除用户', category: 'user' },
    { name: 'user:manage', action: 'manage', subject: 'user', description: '管理用户', category: 'user' },
    { name: 'user:ban', action: 'ban', subject: 'user', description: '封禁用户', category: 'user' },

    // 系统权限
    { name: 'system:stats', action: 'read', subject: 'system_stats', description: '查看系统统计', category: 'system' },
    { name: 'system:manage', action: 'manage', subject: 'system', description: '系统管理', category: 'system' },
    { name: 'system:backup', action: 'backup', subject: 'system', description: '系统备份', category: 'system' },

    // 通知权限
    { name: 'notification:read', action: 'read', subject: 'notification', description: '查看通知', category: 'notification' },
    { name: 'notification:create', action: 'create', subject: 'notification', description: '创建通知', category: 'notification' },
    { name: 'notification:manage', action: 'manage', subject: 'notification', description: '管理通知', category: 'notification' }
  ];

  for (const permission of permissions) {
    try {
      const result = await runQuery(`
        INSERT INTO permissions (name, action, subject, description, category, created_at)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [permission.name, permission.action, permission.subject, permission.description, permission.category, DateTimeUtils.nowUTC()]);
      console.log(`✅ 插入权限: ${permission.description} (ID: ${result.lastID})`);
    } catch (error) {
      console.error(`❌ 插入权限失败 ${permission.name}:`, error.message);
    }
  }
}

// 设置角色权限关联
async function setupRolePermissions() {
  // 角色权限映射
  const rolePermissionMappings = {
    'employee': [
      'contract:create',
      'contract:read',
      'contract:update',
      'notification:read'
    ],
    'county_reviewer': [
      'contract:read',
      'contract:review',
      'contract:approve',
      'contract:reject',
      'notification:read',
      'notification:create'
    ],
    'city_reviewer': [
      'contract:read',
      'contract:review',
      'contract:approve',
      'contract:reject',
      'notification:read',
      'notification:create'
    ],
    'admin': [
      'contract:create',
      'contract:read',
      'contract:update',
      'contract:delete',
      'contract:review',
      'contract:approve',
      'contract:reject',
      'user:create',
      'user:read',
      'user:update',
      'user:delete',
      'user:manage',
      'user:ban',
      'system:stats',
      'system:manage',
      'system:backup',
      'notification:read',
      'notification:create',
      'notification:manage'
    ]
  };

  for (const [roleName, permissionNames] of Object.entries(rolePermissionMappings)) {
    // 获取角色ID
    const role = await getQuery('SELECT id FROM roles WHERE name = ?', [roleName]);
    if (!role) {
      console.error(`❌ 角色不存在: ${roleName}`);
      continue;
    }

    console.log(`🔗 设置角色权限: ${roleName}`);
    let permissionCount = 0;

    for (const permissionName of permissionNames) {
      // 获取权限ID
      const permission = await getQuery('SELECT id FROM permissions WHERE name = ?', [permissionName]);
      if (!permission) {
        console.error(`❌ 权限不存在: ${permissionName}`);
        continue;
      }

      try {
        await runQuery(`
          INSERT INTO role_permissions (role_id, permission_id)
          VALUES (?, ?)
        `, [role.id, permission.id]);
        permissionCount++;
      } catch (error) {
        console.error(`❌ 关联权限失败 ${roleName} -> ${permissionName}:`, error.message);
      }
    }

    console.log(`✅ 角色 ${roleName} 关联了 ${permissionCount} 个权限`);
  }
}

// 执行清理和重置
cleanAndReset();
