#!/usr/bin/env node

/**
 * 验证法规员演示账号脚本
 * 验证新创建的法规员演示账号是否配置正确
 */

const sqlite3 = require('sqlite3').verbose();
const bcrypt = require('bcrypt');
const DATABASE_CONFIG = require('./src/config/database');

// 使用统一的数据库配置
const DB_PATH = DATABASE_CONFIG.path;

console.log('🔍 开始验证法规员演示账号...');

// 创建数据库连接
const db = new sqlite3.Database(DB_PATH, (err) => {
  if (err) {
    console.error('❌ 数据库连接失败:', err.message);
    process.exit(1);
  }
  console.log('✅ 数据库连接成功');
});

// Promise化数据库操作
function getQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
}

function allQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
}

// 验证法规员演示账号
async function verifyLegalOfficerDemo() {
  try {
    console.log('\n📋 验证步骤：');
    
    // 1. 检查用户是否存在
    console.log('1. 检查法规员演示账号...');
    const user = await getQuery('SELECT * FROM users WHERE username = ?', ['fagui_demo']);
    if (user) {
      console.log(`   ✅ 用户存在: ${user.username} (ID: ${user.id})`);
      console.log(`   ✅ 角色: ${user.role}`);
      console.log(`   ✅ 状态: ${user.status}`);
      console.log(`   ✅ 角色ID: ${user.role_id}`);
    } else {
      console.log('   ❌ 法规员演示账号不存在');
      return false;
    }

    // 2. 验证密码
    console.log('\n2. 验证密码...');
    const passwordMatch = await bcrypt.compare('123456', user.password);
    if (passwordMatch) {
      console.log('   ✅ 密码验证通过');
    } else {
      console.log('   ❌ 密码验证失败');
      return false;
    }

    // 3. 检查角色配置
    console.log('\n3. 检查角色配置...');
    const role = await getQuery('SELECT * FROM roles WHERE id = ?', [user.role_id]);
    if (role) {
      console.log(`   ✅ 角色信息: ${role.display_name} (${role.name})`);
      console.log(`   ✅ 角色等级: ${role.level}`);
      console.log(`   ✅ 角色描述: ${role.description}`);
    } else {
      console.log('   ❌ 角色配置不存在');
      return false;
    }

    // 4. 检查权限配置
    console.log('\n4. 检查权限配置...');
    const permissions = await allQuery(`
      SELECT p.name, p.description
      FROM permissions p
      JOIN role_permissions rp ON p.id = rp.permission_id
      WHERE rp.role_id = ?
      ORDER BY p.name
    `, [user.role_id]);
    
    if (permissions.length > 0) {
      console.log(`   ✅ 权限数量: ${permissions.length}`);
      permissions.forEach(perm => {
        console.log(`      - ${perm.name}: ${perm.description}`);
      });
    } else {
      console.log('   ❌ 未找到权限配置');
      return false;
    }

    // 5. 检查必要权限
    console.log('\n5. 检查必要权限...');
    const requiredPermissions = [
      'contract:read',
      'contract:view_pending_number',
      'contract:assign_number',
      'contract:manage_number'
    ];
    
    const userPermissions = permissions.map(p => p.name);
    let allPermissionsPresent = true;
    
    for (const requiredPerm of requiredPermissions) {
      if (userPermissions.includes(requiredPerm)) {
        console.log(`   ✅ ${requiredPerm}`);
      } else {
        console.log(`   ❌ 缺少权限: ${requiredPerm}`);
        allPermissionsPresent = false;
      }
    }

    if (allPermissionsPresent) {
      console.log('\n🎉 法规员演示账号验证通过！');
      console.log('\n📋 账号信息总结：');
      console.log(`   用户名: ${user.username}`);
      console.log(`   密码: 123456`);
      console.log(`   角色: ${role.display_name}`);
      console.log(`   权限数量: ${permissions.length}`);
      console.log(`   状态: ${user.status}`);
      return true;
    } else {
      console.log('\n❌ 权限配置不完整');
      return false;
    }

  } catch (error) {
    console.error('❌ 验证失败:', error.message);
    return false;
  }
}

// 主函数
async function main() {
  try {
    const success = await verifyLegalOfficerDemo();
    if (success) {
      console.log('\n✅ 验证完成，法规员演示账号可以正常使用！');
    } else {
      console.log('\n❌ 验证失败，请检查配置！');
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ 验证执行失败:', error.message);
    process.exit(1);
  } finally {
    db.close((err) => {
      if (err) {
        console.error('❌ 关闭数据库连接失败:', err.message);
      } else {
        console.log('✅ 数据库连接已关闭');
      }
    });
  }
}

// 执行脚本
if (require.main === module) {
  main();
}

module.exports = {
  verifyLegalOfficerDemo
};
