#!/usr/bin/env node

/**
 * 验证RBAC权限系统配置脚本
 */

const sqlite3 = require('sqlite3').verbose();
const DATABASE_CONFIG = require('./src/config/database');

const DB_PATH = DATABASE_CONFIG.path;

console.log('🔍 验证RBAC权限系统配置...\n');

// 创建数据库连接
const db = new sqlite3.Database(DB_PATH, (err) => {
  if (err) {
    console.error('❌ 数据库连接失败:', err.message);
    process.exit(1);
  }
  console.log('✅ 数据库连接成功\n');
});

// Promise化数据库操作
function allQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
}

// 验证RBAC系统
async function verifyRBACSystem() {
  try {
    console.log('📊 1. 角色统计:');
    const roles = await allQuery('SELECT * FROM roles ORDER BY level');
    console.log(`   总计: ${roles.length} 个角色`);
    roles.forEach(role => {
      console.log(`   - ${role.display_name} (${role.name}) - 级别: ${role.level}`);
    });

    console.log('\n📊 2. 权限统计:');
    const permissions = await allQuery('SELECT category, COUNT(*) as count FROM permissions GROUP BY category');
    const totalPermissions = await allQuery('SELECT COUNT(*) as total FROM permissions');
    console.log(`   总计: ${totalPermissions[0].total} 个权限`);
    permissions.forEach(perm => {
      console.log(`   - ${perm.category}: ${perm.count} 个权限`);
    });

    console.log('\n📊 3. 用户角色分配:');
    const users = await allQuery(`
      SELECT u.username, u.role, r.display_name as role_display_name, r.level
      FROM users u
      LEFT JOIN roles r ON u.role_id = r.id
      ORDER BY r.level DESC
    `);
    console.log(`   总计: ${users.length} 个用户`);
    users.forEach(user => {
      console.log(`   - ${user.username}: ${user.role_display_name || user.role} (级别: ${user.level || 'N/A'})`);
    });

    console.log('\n📊 4. 角色权限详情:');
    for (const role of roles) {
      const rolePermissions = await allQuery(`
        SELECT p.name, p.description, p.category
        FROM role_permissions rp
        JOIN permissions p ON rp.permission_id = p.id
        WHERE rp.role_id = ?
        ORDER BY p.category, p.name
      `, [role.id]);

      console.log(`\n   🔑 ${role.display_name} (${rolePermissions.length} 个权限):`);
      
      const permissionsByCategory = {};
      rolePermissions.forEach(perm => {
        if (!permissionsByCategory[perm.category]) {
          permissionsByCategory[perm.category] = [];
        }
        permissionsByCategory[perm.category].push(perm);
      });

      Object.keys(permissionsByCategory).forEach(category => {
        console.log(`     📂 ${category}:`);
        permissionsByCategory[category].forEach(perm => {
          console.log(`       - ${perm.description} (${perm.name})`);
        });
      });
    }

    console.log('\n📊 5. 权限覆盖率检查:');
    const allPermissions = await allQuery('SELECT * FROM permissions ORDER BY category, name');
    const assignedPermissions = await allQuery(`
      SELECT DISTINCT p.id, p.name, p.description
      FROM permissions p
      JOIN role_permissions rp ON p.id = rp.permission_id
    `);

    console.log(`   已分配权限: ${assignedPermissions.length}/${allPermissions.length}`);
    
    const unassignedPermissions = allPermissions.filter(p => 
      !assignedPermissions.some(ap => ap.id === p.id)
    );

    if (unassignedPermissions.length > 0) {
      console.log('   ⚠️  未分配的权限:');
      unassignedPermissions.forEach(perm => {
        console.log(`     - ${perm.description} (${perm.name})`);
      });
    } else {
      console.log('   ✅ 所有权限都已分配');
    }

    console.log('\n🎉 RBAC权限系统验证完成！');

  } catch (error) {
    console.error('❌ 验证失败:', error.message);
  } finally {
    db.close((err) => {
      if (err) {
        console.error('❌ 关闭数据库连接失败:', err.message);
      } else {
        console.log('\n✅ 数据库连接已关闭');
      }
    });
  }
}

// 执行验证
verifyRBACSystem();
