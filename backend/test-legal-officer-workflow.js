#!/usr/bin/env node

/**
 * 法规员工作流程测试脚本
 * 测试完整的合同审核到编号分配流程
 */

const sqlite3 = require('sqlite3').verbose();
const DATABASE_CONFIG = require('./src/config/database');

// 使用统一的数据库配置
const DB_PATH = DATABASE_CONFIG.path;

console.log('🚀 开始测试法规员工作流程...');

// 创建数据库连接
const db = new sqlite3.Database(DB_PATH, (err) => {
  if (err) {
    console.error('❌ 数据库连接失败:', err.message);
    process.exit(1);
  }
  console.log('✅ 数据库连接成功');
});

// Promise化数据库操作
function runQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function (err) {
      if (err) {
        reject(err);
      } else {
        resolve({ changes: this.changes, lastID: this.lastID });
      }
    });
  });
}

function getQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
}

function allQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
}

// 测试工作流程
async function testWorkflow() {
  try {
    console.log('\n📋 测试步骤：');
    
    // 1. 检查法规员用户是否存在
    console.log('1. 检查法规员用户...');
    const legalOfficer = await getQuery('SELECT * FROM users WHERE role = ?', ['legal_officer']);
    if (legalOfficer) {
      console.log(`   ✅ 法规员用户存在: ${legalOfficer.username}`);
    } else {
      console.log('   ❌ 法规员用户不存在');
      return;
    }

    // 2. 检查角色权限配置
    console.log('2. 检查角色权限配置...');
    const rolePermissions = await allQuery(`
      SELECT r.name as role_name, p.name as permission_name
      FROM roles r
      JOIN role_permissions rp ON r.id = rp.role_id
      JOIN permissions p ON rp.permission_id = p.id
      WHERE r.name = 'legal_officer'
    `);
    console.log(`   ✅ 法规员权限数量: ${rolePermissions.length}`);
    rolePermissions.forEach(rp => {
      console.log(`      - ${rp.permission_name}`);
    });

    // 3. 检查合同状态
    console.log('3. 检查合同状态分布...');
    const statusStats = await allQuery(`
      SELECT status, COUNT(*) as count
      FROM contracts
      GROUP BY status
    `);
    console.log('   合同状态统计:');
    statusStats.forEach(stat => {
      console.log(`      - ${stat.status}: ${stat.count}`);
    });

    // 4. 模拟创建一个待分配编号的合同
    console.log('4. 创建测试合同...');
    const now = new Date().toISOString();
    const testContract = await runQuery(`
      INSERT INTO contracts (
        serial_number, submitter_id, reviewer_id, filename, file_path, file_size,
        status, legal_officer_id, reviewed_at, created_at, updated_at, submitted_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      'TEST-2024-001',
      2, // employee用户ID
      4, // city_reviewer用户ID
      'test-contract.pdf',
      '/uploads/test-contract.pdf',
      1024000,
      'pending_contract_number',
      legalOfficer.id,
      now,
      now,
      now,
      now
    ]);
    console.log(`   ✅ 创建测试合同，ID: ${testContract.lastID}`);

    // 5. 查询待分配编号的合同
    console.log('5. 查询待分配编号的合同...');
    const pendingContracts = await allQuery(`
      SELECT c.*, s.username as submitter_name, l.username as legal_officer_name
      FROM contracts c
      LEFT JOIN users s ON c.submitter_id = s.id
      LEFT JOIN users l ON c.legal_officer_id = l.id
      WHERE c.status = 'pending_contract_number'
    `);
    console.log(`   ✅ 待分配编号合同数量: ${pendingContracts.length}`);

    // 6. 模拟分配合同编号
    console.log('6. 模拟分配合同编号...');
    const contractNumber = `HT-2024-${String(testContract.lastID).padStart(4, '0')}`;
    await runQuery(`
      UPDATE contracts
      SET contract_number = ?, status = 'completed', contract_number_assigned_at = ?
      WHERE id = ?
    `, [contractNumber, now, testContract.lastID]);
    console.log(`   ✅ 分配合同编号: ${contractNumber}`);

    // 7. 验证最终状态
    console.log('7. 验证最终状态...');
    const updatedContract = await getQuery(`
      SELECT c.*, s.username as submitter_name, l.username as legal_officer_name
      FROM contracts c
      LEFT JOIN users s ON c.submitter_id = s.id
      LEFT JOIN users l ON c.legal_officer_id = l.id
      WHERE c.id = ?
    `, [testContract.lastID]);
    
    console.log('   ✅ 合同最终状态:');
    console.log(`      - 流水号: ${updatedContract.serial_number}`);
    console.log(`      - 合同编号: ${updatedContract.contract_number}`);
    console.log(`      - 状态: ${updatedContract.status}`);
    console.log(`      - 法规员: ${updatedContract.legal_officer_name}`);
    console.log(`      - 编号分配时间: ${updatedContract.contract_number_assigned_at}`);

    console.log('\n🎉 法规员工作流程测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    throw error;
  }
}

// 主函数
async function main() {
  try {
    await testWorkflow();
  } catch (error) {
    console.error('❌ 测试执行失败:', error.message);
    process.exit(1);
  } finally {
    db.close((err) => {
      if (err) {
        console.error('❌ 关闭数据库连接失败:', err.message);
      } else {
        console.log('✅ 数据库连接已关闭');
      }
    });
  }
}

// 执行测试
if (require.main === module) {
  main();
}

module.exports = {
  testWorkflow
};
