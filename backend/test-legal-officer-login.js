#!/usr/bin/env node

/**
 * 测试法规员登录功能
 * 验证法规员账号是否能正常登录
 */

const axios = require('axios');

// 配置
const BASE_URL = 'http://localhost:5000';
const API_URL = `${BASE_URL}/api`;

// 测试法规员登录
async function testLegalOfficerLogin() {
  try {
    console.log('🚀 开始测试法规员登录功能...');

    // 测试登录
    const loginResponse = await axios.post(`${API_URL}/auth/login`, {
      username: 'legal_officer',
      password: '123456'
    });

    if (loginResponse.data.success) {
      console.log('✅ 法规员登录成功');
      console.log('   用户名:', loginResponse.data.data.user.username);
      console.log('   角色:', loginResponse.data.data.user.role);
      console.log('   状态:', loginResponse.data.data.user.status);
      
      // 测试获取用户信息
      const token = loginResponse.data.data.token;
      const profileResponse = await axios.get(`${API_URL}/auth/profile`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (profileResponse.data.success) {
        console.log('✅ 获取用户信息成功');
        console.log('   用户详情:', profileResponse.data.data);
      } else {
        console.log('❌ 获取用户信息失败');
      }

    } else {
      console.log('❌ 法规员登录失败:', loginResponse.data.message);
    }

  } catch (error) {
    if (error.response) {
      console.log('❌ 登录请求失败:', error.response.data.message || error.response.statusText);
    } else if (error.request) {
      console.log('❌ 无法连接到服务器，请确保后端服务正在运行');
    } else {
      console.log('❌ 测试失败:', error.message);
    }
  }
}

// 主函数
async function main() {
  try {
    await testLegalOfficerLogin();
    console.log('🎉 法规员登录测试完成！');
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    process.exit(1);
  }
}

// 执行测试
if (require.main === module) {
  main();
}

module.exports = {
  testLegalOfficerLogin
};
