#!/usr/bin/env node

/**
 * 创建法规员测试用户脚本
 */

const sqlite3 = require('sqlite3').verbose();
const bcrypt = require('bcrypt');
const DATABASE_CONFIG = require('./src/config/database');

// 使用统一的数据库配置
const DB_PATH = DATABASE_CONFIG.path;

console.log('🚀 开始创建法规员测试用户...');

// 创建数据库连接
const db = new sqlite3.Database(DB_PATH, (err) => {
  if (err) {
    console.error('❌ 数据库连接失败:', err.message);
    process.exit(1);
  }
  console.log('✅ 数据库连接成功');
});

// Promise化数据库操作
function runQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function (err) {
      if (err) {
        reject(err);
      } else {
        resolve({ changes: this.changes, lastID: this.lastID });
      }
    });
  });
}

function getQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
}

// 创建法规员用户
async function createLegalOfficerUser() {
  try {
    // 检查用户是否已存在
    const existingUser = await getQuery('SELECT id FROM users WHERE username = ?', ['legal_officer']);
    if (existingUser) {
      console.log('ℹ️  法规员用户已存在，跳过创建');
      return;
    }

    // 获取法规员角色ID
    const role = await getQuery('SELECT id FROM roles WHERE name = ?', ['legal_officer']);
    if (!role) {
      throw new Error('法规员角色不存在，请先运行迁移脚本');
    }

    // 生成加密密码
    const saltRounds = process.env.NODE_ENV === 'production' ? 6 : 4;
    const password = await bcrypt.hash('123456', saltRounds);

    // 创建用户
    const now = new Date().toISOString();
    const result = await runQuery(`
      INSERT INTO users (username, password, role, role_id, status, created_by, created_at, updated_at)
      VALUES (?, ?, ?, ?, 'active', 1, ?, ?)
    `, ['legal_officer', password, 'legal_officer', role.id, now, now]);

    if (result.changes > 0) {
      console.log('✅ 创建法规员用户成功');
      console.log('   用户名: legal_officer');
      console.log('   密码: 123456');
      console.log('   角色: 市局法规员');
    }

  } catch (error) {
    console.error('❌ 创建法规员用户失败:', error.message);
    throw error;
  }
}

// 主函数
async function main() {
  try {
    await createLegalOfficerUser();
    console.log('🎉 法规员用户创建完成！');
  } catch (error) {
    console.error('❌ 操作失败:', error.message);
    process.exit(1);
  } finally {
    db.close((err) => {
      if (err) {
        console.error('❌ 关闭数据库连接失败:', err.message);
      } else {
        console.log('✅ 数据库连接已关闭');
      }
    });
  }
}

// 执行脚本
if (require.main === module) {
  main();
}

module.exports = {
  createLegalOfficerUser
};
