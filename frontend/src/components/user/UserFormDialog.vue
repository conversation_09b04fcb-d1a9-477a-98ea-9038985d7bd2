<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑用户' : '新增用户'"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      size="default"
    >
      <el-form-item label="用户名" prop="username">
        <el-input
          v-model="formData.username"
          placeholder="请输入用户名"
          :disabled="isEdit"
        />
        <div v-if="!isEdit" class="form-tips">
          <el-icon><InfoFilled /></el-icon>
          <span>用户名只能包含字母、数字和下划线，创建后不可修改</span>
        </div>
      </el-form-item>

      <el-form-item v-if="!isEdit" label="密码" prop="password">
        <el-input
          v-model="formData.password"
          type="password"
          placeholder="请输入密码"
          show-password
        />
      </el-form-item>

      <el-form-item label="角色" prop="role">
        <el-select
          v-model="formData.role"
          placeholder="请选择角色"
          style="width: 100%"
        >
          <el-option label="员工" value="employee" />
          <el-option label="县级审核员" value="county_reviewer" />
          <el-option label="市级审核员" value="city_reviewer" />
          <el-option label="管理员" value="admin" />
        </el-select>
      </el-form-item>

      <el-form-item label="真实姓名" prop="real_name">
        <el-input
          v-model="formData.real_name"
          placeholder="请输入真实姓名（可选）"
        />
      </el-form-item>

      <el-form-item label="邮箱" prop="email">
        <el-input
          v-model="formData.email"
          placeholder="请输入邮箱地址（可选）"
        />
      </el-form-item>

      <el-form-item label="手机号" prop="phone">
        <el-input v-model="formData.phone" placeholder="请输入手机号（可选）" />
      </el-form-item>

      <el-form-item v-if="isEdit" label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio value="active">正常</el-radio>
          <el-radio value="inactive">禁用</el-radio>
          <el-radio value="banned">封禁</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button :disabled="submitting" @click="handleClose">
          取消
        </el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          {{ submitting ? "保存中..." : "确定" }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from "vue";
import { ElMessage } from "element-plus";
import { InfoFilled } from "@element-plus/icons-vue";

import { useUsers } from "@/composables/useUsers";

// 定义 props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  user: {
    type: Object,
    default: null,
  },
});

// 定义 emits
const emit = defineEmits(["update:modelValue", "saved"]);

// 用户管理
const { createUser, updateUser, submitting } = useUsers();

// 对话框显示状态
const dialogVisible = ref(props.modelValue);

// 表单引用
const formRef = ref();

// 是否编辑模式
const isEdit = computed(() => !!props.user?.id);

// 表单数据
const formData = reactive({
  username: "",
  password: "",
  role: "employee",
  real_name: "",
  email: "",
  phone: "",
  status: "active",
});

// 表单验证规则
const formRules = {
  username: [
    { required: true, message: "请输入用户名", trigger: "blur" },
    {
      min: 3,
      max: 50,
      message: "用户名长度必须在3-50个字符之间",
      trigger: "blur",
    },
    {
      pattern: /^[a-zA-Z0-9_]+$/,
      message: "用户名只能包含字母、数字和下划线",
      trigger: "blur",
    },
  ],
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { min: 6, message: "密码长度至少6个字符", trigger: "blur" },
  ],
  role: [{ required: true, message: "请选择角色", trigger: "change" }],
  real_name: [
    { max: 100, message: "真实姓名不能超过100个字符", trigger: "blur" },
  ],
  email: [{ type: "email", message: "邮箱格式不正确", trigger: "blur" }],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: "手机号格式不正确", trigger: "blur" },
  ],
};

// 监听 props 变化
watch(
  () => props.modelValue,
  (newValue) => {
    dialogVisible.value = newValue;
  },
);

watch(
  () => props.user,
  (newUser) => {
    if (newUser) {
      initForm(newUser);
    } else {
      resetForm();
    }
  },
);

// 监听对话框显示状态
watch(
  () => dialogVisible.value,
  (newValue) => {
    emit("update:modelValue", newValue);

    if (newValue) {
      if (props.user) {
        initForm(props.user);
      } else {
        resetForm();
      }
    }
  },
);

// 初始化表单
const initForm = (user) => {
  formData.username = user.username || "";
  formData.password = "";
  formData.role = user.role || "employee";
  formData.real_name = user.real_name || "";
  formData.email = user.email || "";
  formData.phone = user.phone || "";
  formData.status = user.status || "active";
};

// 重置表单
const resetForm = () => {
  formData.username = "";
  formData.password = "";
  formData.role = "employee";
  formData.real_name = "";
  formData.email = "";
  formData.phone = "";
  formData.status = "active";

  if (formRef.value) {
    formRef.value.resetFields();
  }
};

// 处理提交
const handleSubmit = async () => {
  try {
    // 验证表单
    await formRef.value.validate();

    // 构建提交数据
    const submitData = { ...formData };

    // 移除空值
    Object.keys(submitData).forEach((key) => {
      if (submitData[key] === "") {
        delete submitData[key];
      }
    });

    // 编辑模式下移除密码字段
    if (isEdit.value) {
      delete submitData.password;
    }

    // 提交数据
    if (isEdit.value) {
      await updateUser(props.user.id, submitData);
      ElMessage.success("用户更新成功");
    } else {
      await createUser(submitData);
      ElMessage.success("用户创建成功");
    }

    emit("saved");
    dialogVisible.value = false;
  } catch (error) {
    // API错误由全局处理器处理，这里只需要阻止对话框关闭
    // 表单验证错误会被自动处理
  }
};

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false;
};
</script>

<style scoped>
.form-tips {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

.form-tips .el-icon {
  color: #409eff;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* Element Plus 样式覆盖 */
:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px 20px;
  border-top: 1px solid #e4e7ed;
}
</style>
