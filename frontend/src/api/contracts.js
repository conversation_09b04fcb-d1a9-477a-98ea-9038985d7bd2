/**
 * 合同相关 API
 * 处理合同提交、查询、修改、审核等功能
 */

import api from "./request";
import { formatTime } from "@/utils/dateUtils";

// 合同 API 接口
export const contractsAPI = {
  /**
   * 提交合同
   * @param {Object} contractData - 合同数据
   * @returns {Promise} 提交结果
   */
  submit(contractData) {
    return api.post("/contracts", contractData);
  },

  /**
   * 获取合同列表
   * @param {Object} params - 查询参数
   * @returns {Promise} 合同列表
   */
  getList(params = {}) {
    return api.get("/contracts", params);
  },

  /**
   * 获取合同详情
   * @param {number} id - 合同ID
   * @returns {Promise} 合同详情
   */
  getDetail(id) {
    return api.get(`/contracts/${id}`);
  },

  /**
   * 修改合同
   * @param {number} id - 合同ID
   * @param {Object} contractData - 合同数据
   * @returns {Promise} 修改结果
   */
  update(id, contractData) {
    return api.put(`/contracts/${id}`, contractData);
  },

  /**
   * 删除合同
   * @param {number} id - 合同ID
   * @returns {Promise} 删除结果
   */
  delete(id) {
    return api.delete(`/contracts/${id}`);
  },

  /**
   * 获取合同统计信息
   * @returns {Promise} 统计数据
   */
  getStats() {
    return api.get("/contracts/stats");
  },

  /**
   * 提交审核结果
   * @param {number} id - 合同ID
   * @param {Object} reviewData - 审核数据
   * @returns {Promise} 审核结果
   */
  submitReview(id, reviewData) {
    return api.put(`/contracts/${id}/review`, reviewData);
  },

  /**
   * 获取我的合同列表（员工）
   * @param {Object} params - 查询参数
   * @returns {Promise} 合同列表
   */
  getMyContracts(params = {}) {
    return this.getList({
      ...params,
      my: true,
    });
  },

  /**
   * 获取待审核合同列表（审核员）
   * @param {Object} params - 查询参数
   * @returns {Promise} 合同列表
   */
  getPendingReview(params = {}) {
    return api.get("/contracts/pending", { params });
  },

  /**
   * 获取已审核合同列表（审核员）
   * @param {Object} params - 查询参数
   * @returns {Promise} 合同列表
   */
  getReviewed(params = {}) {
    return api.get("/contracts/reviewed", { params });
  },

  /**
   * 批量操作合同
   * @param {Array} ids - 合同ID数组
   * @param {string} action - 操作类型
   * @param {Object} data - 操作数据
   * @returns {Promise} 操作结果
   */
  batchOperation(ids, action, data = {}) {
    return api.post("/contracts/batch", {
      ids,
      action,
      data,
    });
  },

  /**
   * 导出合同列表
   * @param {Object} params - 查询参数
   * @returns {Promise} 导出文件
   */
  export(params = {}) {
    return api.download("/contracts/export", params);
  },

  /**
   * 获取审核员列表
   * @param {Object} params - 查询参数，包含level字段
   * @returns {Promise} 审核员列表
   */
  getReviewers(params = {}) {
    // 构建查询字符串，避免axios参数序列化问题
    const queryString = new URLSearchParams(params).toString();
    const url = queryString
      ? `/contracts/reviewers?${queryString}`
      : "/contracts/reviewers";
    return api.get(url);
  },

  /**
   * 重新提交合同（审核不通过后）
   * @param {number} id - 合同ID
   * @param {Object} contractData - 合同数据
   * @returns {Promise} 提交结果
   */
  resubmit(id, contractData) {
    return this.update(id, {
      ...contractData,
      resubmit: true,
    });
  },

  /**
   * 获取合同历史记录
   * @param {number} id - 合同ID
   * @returns {Promise} 历史记录
   */
  getHistory(id) {
    return api.get(`/contracts/${id}/history`);
  },

  /**
   * 获取合同审核日志
   * @param {number} id - 合同ID
   * @returns {Promise} 审核日志
   */
  getReviewLog(id) {
    return api.get(`/contracts/${id}/review-log`);
  },
};

// 导入状态管理
import {
  CONTRACT_STATUS,
  STATUS_LABELS,
  STATUS_COLORS,
  ContractStatusManager,
} from "@/utils/contractStatus";

// 重新导出常量以保持兼容性
export { CONTRACT_STATUS };
export const CONTRACT_STATUS_TEXT = STATUS_LABELS;
export const CONTRACT_STATUS_COLOR = STATUS_COLORS;

// 工具函数
export const contractUtils = {
  /**
   * 格式化合同状态
   * @param {string} status - 状态值
   * @returns {string} 状态文本
   */
  formatStatus(status) {
    return ContractStatusManager.formatStatus(status);
  },

  /**
   * 获取状态颜色
   * @param {string} status - 状态值
   * @returns {string} 颜色类型
   */
  getStatusColor(status) {
    return ContractStatusManager.getStatusColor(status);
  },

  /**
   * 获取状态图标
   * @param {string} status - 状态值
   * @returns {string} 图标名称
   */
  getStatusIcon(status) {
    return ContractStatusManager.getStatusIcon(status);
  },

  /**
   * 检查是否可以修改
   * @param {Object} contract - 合同对象
   * @param {Object} user - 用户对象
   * @returns {boolean} 是否可以修改
   */
  canModify(contract, user) {
    return ContractStatusManager.canModifyContract(contract, user);
  },

  /**
   * 检查是否可以审核
   * @param {Object} contract - 合同对象
   * @param {Object} user - 用户对象
   * @returns {boolean} 是否可以审核
   */
  canReview(contract, user) {
    return ContractStatusManager.canReviewContract(contract, user);
  },

  /**
   * 检查是否可以删除
   * @param {Object} contract - 合同对象
   * @param {Object} user - 用户对象
   * @returns {boolean} 是否可以删除
   */
  canDelete(contract, user) {
    return ContractStatusManager.canDeleteContract(contract, user);
  },

  /**
   * 检查状态是否可以流转
   * @param {string} fromStatus - 当前状态
   * @param {string} toStatus - 目标状态
   * @returns {boolean} 是否可以流转
   */
  canTransition(fromStatus, toStatus) {
    return ContractStatusManager.canTransition(fromStatus, toStatus);
  },

  /**
   * 获取状态描述
   * @param {string} status - 状态值
   * @returns {string} 状态描述
   */
  getStatusDescription(status) {
    return ContractStatusManager.getStatusDescription(status);
  },

  /**
   * 获取下一步操作提示
   * @param {Object} contract - 合同对象
   * @param {Object} user - 用户对象
   * @returns {string} 操作提示
   */
  getNextActionHint(contract, user) {
    return ContractStatusManager.getNextActionHint(contract, user);
  },

  /**
   * 检查是否可以查看
   * @param {Object} contract - 合同对象
   * @param {Object} user - 用户对象
   * @returns {boolean} 是否可以查看
   */
  canView(contract, user) {
    if (!contract || !user) return false;

    // 管理员可以查看所有合同
    if (user.role === "admin") return true;

    // 提交人可以查看自己的合同
    if (contract.submitter_id === user.id) return true;

    // 审核员可以查看分配给自己的合同
    if (
      ["reviewer", "county_reviewer", "city_reviewer"].includes(user.role) &&
      contract.reviewer_id === user.id
    )
      return true;

    return false;
  },

  /**
   * 格式化文件大小
   * @param {number} bytes - 字节数
   * @returns {string} 格式化后的大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return "0 Bytes";

    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  },

  /**
   * 格式化日期时间
   * @param {string} dateTime - 日期时间字符串（UTC时间）
   * @returns {string} 格式化后的北京时间
   */
  formatDateTime(dateTime) {
    if (!dateTime) return "-";

    // 使用统一的时区转换工具，将UTC时间转换为北京时间显示
    return formatTime(dateTime);
  },

  /**
   * 获取相对时间
   * @param {string} dateTime - 日期时间字符串
   * @returns {string} 相对时间描述
   */
  getRelativeTime(dateTime) {
    if (!dateTime) return "-";

    const now = new Date();
    const date = new Date(dateTime);
    const diff = now - date;

    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}天前`;
    if (hours > 0) return `${hours}小时前`;
    if (minutes > 0) return `${minutes}分钟前`;
    return "刚刚";
  },

  /**
   * 获取待分配编号的合同列表
   * @param {Object} params - 查询参数
   * @returns {Promise} 合同列表
   */
  getPendingContractNumber(params = {}) {
    return api.get("/contracts/pending-number", params);
  },

  /**
   * 分配合同编号
   * @param {number} id - 合同ID
   * @param {Object} data - 分配数据
   * @returns {Promise} 分配结果
   */
  assignContractNumber(id, data) {
    return api.put(`/contracts/${id}/assign-number`, data);
  },
};

export default contractsAPI;
