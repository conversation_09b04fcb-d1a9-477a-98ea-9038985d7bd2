/**
 * HTTP 请求封装
 * 基于 Axios 的请求拦截器和响应处理
 * 支持自动重试、请求取消、离线检测等功能
 */

import axios from "axios";
import { ElMessage, ElNotification } from "element-plus";
import router from "@/router";

// 请求队列管理
const pendingRequests = new Map();
let isOffline = false;

// 创建 axios 实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || "/api",
  timeout: 15000, // 增加超时时间
  headers: {
    "Content-Type": "application/json",
  },
});

// 生成请求唯一标识
const generateRequestKey = (config) => {
  return `${config.method}:${config.url}:${JSON.stringify(config.params || {})}:${JSON.stringify(config.data || {})}`;
};

// 取消重复请求
const cancelDuplicateRequest = (config) => {
  // 文件上传请求不进行重复检测，避免误杀长时间上传
  const isFileUpload =
    config.headers &&
    (config.headers["Content-Type"] === "multipart/form-data" ||
      config.url?.includes("/upload") ||
      config.url?.includes("/chunk"));

  // Dashboard 相关请求不进行重复检测，避免误杀并发请求
  const isDashboardRequest = config.url?.includes("/dashboard/");

  if (isFileUpload || isDashboardRequest) {
    return null; // 不进行重复请求检测
  }

  const requestKey = generateRequestKey(config);

  if (pendingRequests.has(requestKey)) {
    const existingRequest = pendingRequests.get(requestKey);
    // 检查请求是否是在很短时间内发起的（100ms内），如果是则取消
    const now = Date.now();
    if (now - existingRequest.timestamp < 100) {
      existingRequest.source.cancel("取消重复请求");
      pendingRequests.delete(requestKey);
    }
  }

  const source = axios.CancelToken.source();
  config.cancelToken = source.token;
  pendingRequests.set(requestKey, {
    source,
    timestamp: Date.now(),
  });

  return requestKey;
};

// 移除已完成的请求
const removePendingRequest = (config) => {
  if (!config || !config._requestKey) {
    return; // 文件上传请求或dashboard请求没有requestKey
  }

  const requestKey = config._requestKey;
  if (pendingRequests.has(requestKey)) {
    pendingRequests.delete(requestKey);
  }
};

// 网络状态检测
const checkNetworkStatus = () => {
  const wasOffline = isOffline;
  isOffline = !navigator.onLine;

  if (wasOffline && !isOffline) {
    ElNotification.success({
      title: "网络已恢复",
      message: "网络连接已恢复正常",
      duration: 3000,
    });
  } else if (!wasOffline && isOffline) {
    ElNotification.warning({
      title: "网络连接断开",
      message: "请检查网络连接",
      duration: 0, // 不自动关闭
    });
  }

  return !isOffline;
};

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 检查是否正在登出，如果是则阻止新的API请求
    if (window.isLoggingOut) {
      return Promise.reject(new Error("用户正在登出"));
    }

    // 检查网络状态
    if (!checkNetworkStatus()) {
      return Promise.reject(new Error("网络连接不可用"));
    }

    // 取消重复请求
    const requestKey = cancelDuplicateRequest(config);
    config._requestKey = requestKey; // 可能为null（文件上传请求）

    // 添加认证令牌（登录和注册请求除外）
    const isAuthRequest =
      config.url?.includes("/auth/login") ||
      config.url?.includes("/auth/register");
    if (!isAuthRequest) {
      const token = localStorage.getItem("token");
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }

    // 添加请求时间戳
    config.metadata = {
      startTime: Date.now(),
      requestKey,
    };

    // 显示加载状态
    if (config.showLoading !== false) {
      if (window.showGlobalLoading) {
        window.showGlobalLoading("请求处理中...");
      }
    }

    // 请求日志 (开发环境)
    if (import.meta.env.DEV) {
      console.log(`🚀 API请求: ${config.method?.toUpperCase()} ${config.url}`, {
        params: config.params,
        data: config.data,
        headers: config.headers,
      });
    }

    return config;
  },
  (error) => {
    // 隐藏加载状态
    if (window.hideGlobalLoading) {
      window.hideGlobalLoading();
    }

    if (import.meta.env.DEV) {
      console.error("❌ 请求拦截器错误:", error);
    }

    return Promise.reject(error);
  },
);

// 自动重试配置
const retryConfig = {
  maxRetries: 3,
  retryDelay: 1000,
  retryCondition: (error) => {
    // 网络错误或5xx错误才重试
    return (
      !error.response ||
      (error.response.status >= 500 && error.response.status < 600)
    );
  },
};

// 重试函数
const retryRequest = async (originalConfig, retryCount = 0) => {
  if (retryCount >= retryConfig.maxRetries) {
    throw originalConfig._lastError || new Error("请求重试次数已达上限");
  }

  // 等待重试延时
  await new Promise((resolve) =>
    setTimeout(resolve, retryConfig.retryDelay * Math.pow(2, retryCount)),
  );

  try {
    // 重新生成请求key，避免被取消
    const newRequestKey = cancelDuplicateRequest(originalConfig);
    originalConfig._requestKey = newRequestKey;
    originalConfig.metadata.retryCount = retryCount + 1;

    return await request(originalConfig);
  } catch (error) {
    originalConfig._lastError = error;
    return retryRequest(originalConfig, retryCount + 1);
  }
};

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    // 移除已完成的请求
    removePendingRequest(response.config);

    // 隐藏加载状态
    if (window.hideGlobalLoading) {
      window.hideGlobalLoading();
    }

    // 检查是否有新令牌
    const newToken = response.headers["x-new-token"];
    if (newToken) {
      localStorage.setItem("token", newToken);
    }

    // 计算请求耗时
    const duration = response.config.metadata
      ? Date.now() - response.config.metadata.startTime
      : 0;

    // 响应日志 (开发环境)
    if (import.meta.env.DEV) {
      console.log(
        `✅ API响应: ${response.config.method?.toUpperCase()} ${response.config.url}`,
        {
          status: response.status,
          duration: `${duration}ms`,
          data: response.data,
        },
      );
    }

    // 统一处理响应数据
    const { data } = response;

    // 如果后端返回的是标准格式
    if (data && typeof data === "object" && "success" in data) {
      if (data.success) {
        return data;
      } else {
        // 业务错误
        const errorMessage = data.message || "操作失败";
        ElMessage.error(errorMessage);
        return Promise.reject(new Error(errorMessage));
      }
    }

    // 直接返回数据
    return data;
  },
  async (error) => {
    // 移除已完成的请求
    if (error.config) {
      removePendingRequest(error.config);
    }

    // 隐藏加载状态
    if (window.hideGlobalLoading) {
      window.hideGlobalLoading();
    }

    // 请求被取消
    if (axios.isCancel(error)) {
      if (import.meta.env.DEV) {
        console.log("🚫 请求已取消:", error.message);
      }
      return Promise.reject(error);
    }

    // 如果是用户登出导致的请求阻止，不记录错误日志
    if (error.message === "用户正在登出") {
      if (import.meta.env.DEV) {
        console.log("🚪 登出过程中阻止API请求:", error.config?.url);
      }
      return Promise.reject(error);
    }

    // 计算请求耗时
    const duration = error.config?.metadata
      ? Date.now() - error.config.metadata.startTime
      : 0;

    // 开发环境输出详细错误信息
    if (import.meta.env.DEV) {
      console.error("❌ API错误:", {
        url: error.config?.url,
        method: error.config?.method,
        duration: `${duration}ms`,
        status: error.response?.status,
        message: error.message,
        error,
      });
    }

    // 网络错误或超时 - 尝试重试
    if (!error.response) {
      if (error.code === "ECONNABORTED") {
        ElMessage.error("请求超时，请稍后重试");
      } else if (
        retryConfig.retryCondition(error) &&
        error.config &&
        !error.config._isRetry
      ) {
        try {
          error.config._isRetry = true;
          return await retryRequest(error.config);
        } catch (retryError) {
          ElMessage.error("网络连接失败，请检查网络设置");
          return Promise.reject(retryError);
        }
      } else {
        ElMessage.error("网络连接失败，请检查网络设置");
      }
      return Promise.reject(error);
    }

    const { status, data } = error.response;
    let errorMessage = "请求失败";
    let shouldShowMessage = true;

    // 根据状态码处理错误
    switch (status) {
      case 400:
        errorMessage = data?.message || "请求参数错误";
        break;
      case 401:
        // 区分登录请求和其他请求的401错误
        const isLoginRequest = error.config?.url?.includes("/auth/login");
        if (isLoginRequest) {
          errorMessage = data?.message || "用户名或密码错误";
        } else {
          errorMessage = "登录已过期，请重新登录";
          // 清除本地存储的认证信息
          localStorage.removeItem("token");
          localStorage.removeItem("user");
          // 跳转到登录页
          router.push("/login");
        }
        break;
      case 403:
        errorMessage = data?.message || "权限不足";
        break;
      case 404:
        errorMessage = data?.message || "请求的资源不存在";
        break;
      case 409:
        errorMessage = data?.message || "数据冲突";
        break;
      case 422:
        errorMessage = data?.message || "数据验证失败";
        break;
      case 429:
        errorMessage = data?.message || "请求过于频繁，请稍后再试";
        break;
      case 500:
      case 502:
      case 503:
        // 服务器错误 - 尝试重试
        if (
          retryConfig.retryCondition(error) &&
          error.config &&
          !error.config._isRetry
        ) {
          try {
            error.config._isRetry = true;
            return await retryRequest(error.config);
          } catch (retryError) {
            errorMessage =
              status === 500
                ? "服务器内部错误"
                : status === 502
                  ? "网关错误"
                  : "服务暂时不可用";
          }
        } else {
          errorMessage =
            status === 500
              ? "服务器内部错误"
              : status === 502
                ? "网关错误"
                : "服务暂时不可用";
        }
        break;
      default:
        errorMessage = data?.message || `请求失败 (${status})`;
    }

    // 显示错误消息
    if (shouldShowMessage) {
      ElMessage.error(errorMessage);
    }

    // 返回标准化错误信息
    return Promise.reject({
      status,
      message: errorMessage,
      data: data,
      originalError: error,
    });
  },
);

// 请求方法封装
const api = {
  // GET 请求
  get(url, params = {}, config = {}) {
    return request({
      method: "GET",
      url,
      params,
      ...config,
    });
  },

  // POST 请求
  post(url, data = {}, config = {}) {
    return request({
      method: "POST",
      url,
      data,
      ...config,
    });
  },

  // PUT 请求
  put(url, data = {}, config = {}) {
    return request({
      method: "PUT",
      url,
      data,
      ...config,
    });
  },

  // DELETE 请求
  delete(url, config = {}) {
    return request({
      method: "DELETE",
      url,
      ...config,
    });
  },

  // PATCH 请求
  patch(url, data = {}, config = {}) {
    return request({
      method: "PATCH",
      url,
      data,
      ...config,
    });
  },

  // 文件上传
  upload(url, formData, config = {}) {
    return request({
      method: "POST",
      url,
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
      timeout: 300000, // 5分钟超时，适合文件上传
      showLoading: false, // 文件上传不显示全局loading
      ...config,
    });
  },

  // 文件下载
  download(url, params = {}, config = {}) {
    return request({
      method: "GET",
      url,
      params,
      responseType: "blob",
      ...config,
    });
  },

  // 取消所有待处理的请求
  cancelAllRequests() {
    pendingRequests.forEach((requestData, requestKey) => {
      if (requestData && requestData.source) {
        requestData.source.cancel("批量取消请求");
      }
    });
    pendingRequests.clear();
  },

  // 取消特定请求
  cancelRequest(requestKey) {
    if (pendingRequests.has(requestKey)) {
      const cancelToken = pendingRequests.get(requestKey);
      cancelToken.cancel("手动取消请求");
      pendingRequests.delete(requestKey);
    }
  },

  // 获取待处理请求数量
  getPendingRequestsCount() {
    return pendingRequests.size;
  },

  // 检查网络状态
  isOnline() {
    return !isOffline;
  },
};

// 取消请求的方法 (保持向后兼容)
export const createCancelToken = () => {
  return axios.CancelToken.source();
};

// 判断是否为取消请求的错误
export const isCancel = (error) => {
  return axios.isCancel(error);
};

// 判断是否为可忽略的错误（取消请求、登出等）
export const isIgnorableError = (error) => {
  return (
    axios.isCancel(error) ||
    error?.name === "CanceledError" ||
    error?.code === "ERR_CANCELED" ||
    error?.message?.includes("取消") ||
    error?.message === "用户正在登出" ||
    error?.message === "网络连接不可用"
  );
};

// 监听网络状态变化
window.addEventListener("online", () => {
  checkNetworkStatus();
});

window.addEventListener("offline", () => {
  checkNetworkStatus();
});

// 页面卸载时取消所有请求
window.addEventListener("beforeunload", () => {
  api.cancelAllRequests();
});

// 暴露取消请求方法到全局，供登出时使用
window.cancelAllRequests = api.cancelAllRequests;

// 导出请求实例和API方法
export { request };
export default api;
