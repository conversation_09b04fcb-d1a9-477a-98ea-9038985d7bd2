<template>
  <div class="contract-manage-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">合同管理</h1>
        <p class="page-subtitle">管理系统中的所有合同</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="exportContracts">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <div class="stats-grid">
        <div class="stat-card stat-card--primary">
          <div class="stat-icon">
            <el-icon :size="32"><Document /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.total || 0 }}</div>
            <div class="stat-label">总合同数</div>
          </div>
        </div>

        <div class="stat-card stat-card--warning">
          <div class="stat-icon">
            <el-icon :size="32"><Clock /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.pending || 0 }}</div>
            <div class="stat-label">待审核</div>
          </div>
        </div>

        <div class="stat-card stat-card--info">
          <div class="stat-icon">
            <el-icon :size="32"><Loading /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.reviewing || 0 }}</div>
            <div class="stat-label">审核中</div>
          </div>
        </div>

        <div class="stat-card stat-card--success">
          <div class="stat-icon">
            <el-icon :size="32"><Check /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.approved || 0 }}</div>
            <div class="stat-label">已通过</div>
          </div>
        </div>

        <div class="stat-card stat-card--danger">
          <div class="stat-icon">
            <el-icon :size="32"><Close /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.rejected || 0 }}</div>
            <div class="stat-label">已拒绝</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="page-content">
      <el-card class="list-card">
        <ContractList
          :show-selection="true"
          filter-role="admin"
          @view-contract="handleViewContract"
          @view-contract-tab="handleViewContractInTab"
          @edit-contract="handleEditContract"
          @review-contract="handleReviewContract"
          @delete-contract="handleDeleteContract"
          @selection-change="handleSelectionChange"
          @row-click="handleRowClick"
        >
          <template #actions>
            <el-button
              v-if="selectedContracts.length > 0"
              type="danger"
              @click="batchDelete"
            >
              批量删除 ({{ selectedContracts.length }})
            </el-button>
          </template>
        </ContractList>
      </el-card>
    </div>

    <!-- 合同详情对话框 -->
    <ContractDetailDialog
      v-model="showDetailDialog"
      :contract-id="currentContractId"
      @updated="handleContractUpdated"
    />

    <!-- 合同编辑对话框 -->
    <ContractEditDialog
      v-model="showEditDialog"
      :contract="currentContract"
      @updated="handleContractUpdated"
    />

    <!-- 合同审核对话框 -->
    <ContractReviewDialog
      v-model="showReviewDialog"
      :contract="currentContract"
      @reviewed="handleContractReviewed"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Download,
  Document,
  Clock,
  Loading,
  Check,
  Close,
} from "@element-plus/icons-vue";

import ContractList from "@/components/contract/ContractList.vue";
import ContractDetailDialog from "@/components/contract/ContractDetailDialog.vue";
import ContractEditDialog from "@/components/contract/ContractEditDialog.vue";
import ContractReviewDialog from "@/components/contract/ContractReviewDialog.vue";

import { useContracts } from "@/composables/useContracts";
import { useTabs } from "@/composables/useTabs";

// 合同管理
const { stats, getStats, deleteContract } = useContracts();

// Tab管理
const { openTab } = useTabs();

// 对话框状态
const showDetailDialog = ref(false);
const showEditDialog = ref(false);
const showReviewDialog = ref(false);

// 当前操作的合同
const currentContract = ref(null);
const currentContractId = ref(null);

// 选中的合同
const selectedContracts = ref([]);

// 查看合同详情（弹窗方式）
const handleViewContract = (contract) => {
  currentContractId.value = contract.id;
  showDetailDialog.value = true;
};

// 查看合同详情（Tab方式）
const handleViewContractInTab = (contract) => {
  openTab({
    key: `contract-detail-${contract.id}`,
    title: `合同详情 - ${contract.serial_number}`,
    component: "ContractDetailTab",
    icon: "Document",
    params: {
      contractId: contract.id,
      contract: contract,
    },
  });
};

// 编辑合同
const handleEditContract = (contract) => {
  currentContract.value = contract;
  showEditDialog.value = true;
};

// 审核合同
const handleReviewContract = (contract) => {
  currentContract.value = contract;
  showReviewDialog.value = true;
};

// 删除合同
const handleDeleteContract = (contract) => {
  // 删除逻辑在 ContractList 组件中处理，统计数据已在组件内部更新
  // 不再调用 refreshStats()，避免不必要的API请求
};

// 处理选择变化
const handleSelectionChange = (selection) => {
  selectedContracts.value = selection;
};

// 处理行点击 - 直接跳转到Tab页面
const handleRowClick = (row) => {
  handleViewContractInTab(row);
};

// 批量删除
const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedContracts.value.length} 个合同吗？`,
      "批量删除确认",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      },
    );

    const deletePromises = selectedContracts.value.map((contract) =>
      deleteContract(contract.id),
    );

    await Promise.all(deletePromises);

    ElMessage.success(`成功删除 ${selectedContracts.value.length} 个合同`);
    selectedContracts.value = [];
    refreshStats();
  } catch (error) {
    if (error !== "cancel") {
      if (import.meta.env.DEV) {
        console.error("批量删除失败:", error);
      }
      ElMessage.error("批量删除失败");
    }
  }
};

// 导出合同数据
const exportContracts = async () => {
  try {
    ElMessage.info("导出功能开发中...");
    // const blob = await contractsAPI.export()
    // 处理文件下载
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error("导出失败:", error);
    }
    ElMessage.error("导出失败");
  }
};

// 处理合同更新
const handleContractUpdated = () => {
  refreshStats();
  // 刷新列表在各个组件内部处理
};

// 处理合同审核
const handleContractReviewed = () => {
  refreshStats();
  // 刷新列表在各个组件内部处理
};

// 刷新统计数据
const refreshStats = async () => {
  await getStats();
};

// 组件挂载时获取统计数据
onMounted(() => {
  refreshStats();
});
</script>

<style scoped>
.contract-manage-page {
  min-height: 100%;
  background: #f5f5f5;
}

.page-header {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px;
}

.page-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.header-right {
  display: flex;
  gap: 12px;
}

.stats-section {
  padding: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}

.stat-card--primary .stat-icon {
  background: linear-gradient(135deg, #409eff, #337ecc);
}

.stat-card--warning .stat-icon {
  background: linear-gradient(135deg, #e6a23c, #b88230);
}

.stat-card--info .stat-icon {
  background: linear-gradient(135deg, #909399, #73767a);
}

.stat-card--success .stat-icon {
  background: linear-gradient(135deg, #67c23a, #529b2e);
}

.stat-card--danger .stat-icon {
  background: linear-gradient(135deg, #f56c6c, #c45656);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.page-content {
  padding: 0 24px 24px;
}

.list-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .header-right {
    width: 100%;
    justify-content: flex-end;
  }

  .stats-section {
    padding: 16px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .stat-card {
    padding: 16px;
  }

  .page-content {
    padding: 0 16px 16px;
  }
}
</style>
