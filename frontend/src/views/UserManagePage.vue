<template>
  <div class="user-manage-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">用户管理</h1>
        <p class="page-subtitle">管理系统中的所有用户账号</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showCreateDialog">
          <el-icon><Plus /></el-icon>
          新增用户
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filter-section">
      <el-card>
        <div class="filter-form">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索用户名或姓名"
            style="width: 300px"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>

          <el-select
            v-model="roleFilter"
            placeholder="角色筛选"
            style="width: 150px"
            clearable
            @change="handleFilter"
          >
            <el-option label="全部角色" value="" />
            <el-option label="员工" value="employee" />
            <el-option label="县级审核员" value="county_reviewer" />
            <el-option label="市级审核员" value="city_reviewer" />
            <el-option label="管理员" value="admin" />
          </el-select>

          <el-select
            v-model="statusFilter"
            placeholder="状态筛选"
            style="width: 150px"
            clearable
            @change="handleFilter"
          >
            <el-option label="全部状态" value="" />
            <el-option label="正常" value="active" />
            <el-option label="禁用" value="inactive" />
            <el-option label="封禁" value="banned" />
          </el-select>

          <el-button :loading="loading" @click="refreshList">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 用户列表 -->
    <div class="page-content">
      <el-card class="list-card">
        <el-table
          :data="users"
          :loading="loading"
          stripe
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />

          <el-table-column prop="id" label="ID" width="80" align="center" />

          <el-table-column label="头像" width="80" align="center">
            <template #default="{ row }">
              <div class="user-avatar">
                <img
                  v-if="row.avatar"
                  :src="getAvatarUrl(row.avatar)"
                  :alt="row.username"
                  class="avatar-image"
                  style="
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    object-fit: cover;
                  "
                />
                <div
                  v-else
                  class="avatar-placeholder"
                  style="
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    background: #f0f0f0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #999;
                    font-size: 14px;
                  "
                >
                  {{ row.username?.charAt(0)?.toUpperCase() || "U" }}
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="username" label="用户名" width="150" />

          <el-table-column prop="real_name" label="真实姓名" width="120">
            <template #default="{ row }">
              {{ row.real_name || "-" }}
            </template>
          </el-table-column>

          <el-table-column prop="role" label="角色" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getRoleColor(row.role)" size="small">
                {{ formatRole(row.role) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            prop="status"
            label="状态"
            width="100"
            align="center"
          >
            <template #default="{ row }">
              <el-tag :type="getStatusColor(row.status)" size="small">
                {{ formatStatus(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="email" label="邮箱" width="200">
            <template #default="{ row }">
              {{ row.email || "-" }}
            </template>
          </el-table-column>

          <el-table-column prop="phone" label="手机号" width="130">
            <template #default="{ row }">
              {{ row.phone || "-" }}
            </template>
          </el-table-column>

          <el-table-column
            prop="created_at"
            label="创建时间"
            width="160"
            align="center"
          >
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            width="200"
            align="center"
            fixed="right"
          >
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button
                  type="primary"
                  size="small"
                  text
                  @click="editUser(row)"
                >
                  编辑
                </el-button>

                <el-button
                  type="warning"
                  size="small"
                  text
                  @click="resetPassword(row)"
                >
                  重置密码
                </el-button>

                <el-button
                  v-if="row.status === 'active'"
                  type="danger"
                  size="small"
                  text
                  :disabled="row.id === currentUserId"
                  @click="banUser(row)"
                >
                  封禁
                </el-button>

                <el-button
                  v-else
                  type="success"
                  size="small"
                  text
                  @click="unbanUser(row)"
                >
                  解封
                </el-button>

                <el-button
                  type="danger"
                  size="small"
                  text
                  :disabled="row.id === currentUserId"
                  @click="deleteUser(row)"
                >
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 新增/编辑用户对话框 -->
    <UserFormDialog
      v-model="showUserDialog"
      :user="selectedUser"
      @saved="handleUserSaved"
    />

    <!-- 重置密码对话框 -->
    <ResetPasswordDialog
      v-model="showPasswordDialog"
      :user="selectedUser"
      @reset="handlePasswordReset"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus, Search, Refresh } from "@element-plus/icons-vue";

import UserFormDialog from "@/components/user/UserFormDialog.vue";
import ResetPasswordDialog from "@/components/user/ResetPasswordDialog.vue";
import { useUsers } from "@/composables/useUsers";
import { useAuth } from "@/composables/useAuth";
import { getAvatarUrl } from "@/utils/config";

// 认证信息
const { user: currentUser } = useAuth();
const currentUserId = computed(() => currentUser.value?.id);

// 用户管理
const {
  loading,
  users,
  pagination,
  getUsers,
  deleteUser: deleteUserAPI,
  banUser: banUserAPI,
  unbanUser: unbanUserAPI,
} = useUsers();

// 搜索和筛选
const searchKeyword = ref("");
const roleFilter = ref("");
const statusFilter = ref("");

// 分页
const currentPage = computed({
  get: () => pagination.page,
  set: (value) => (pagination.page = value),
});

const pageSize = computed({
  get: () => pagination.pageSize,
  set: (value) => (pagination.pageSize = value),
});

const total = computed(() => pagination.total);

// 对话框状态
const showUserDialog = ref(false);
const showPasswordDialog = ref(false);
const selectedUser = ref(null);

// 选中的用户
const selectedUsers = ref([]);

// 格式化角色
const formatRole = (role) => {
  const roleMap = {
    employee: "员工",
    county_reviewer: "县级审核员",
    city_reviewer: "市级审核员",
    admin: "管理员",
  };
  return roleMap[role] || role;
};

// 获取角色颜色
const getRoleColor = (role) => {
  const colorMap = {
    employee: "info",
    reviewer: "warning",
    admin: "danger",
  };
  return colorMap[role] || "info";
};

// 格式化状态
const formatStatus = (status) => {
  const statusMap = {
    active: "正常",
    inactive: "禁用",
    banned: "封禁",
  };
  return statusMap[status] || status;
};

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    active: "success",
    inactive: "warning",
    banned: "danger",
  };
  return colorMap[status] || "info";
};

// 格式化时间
const formatDateTime = (datetime) => {
  return new Date(datetime).toLocaleString("zh-CN");
};

// 处理搜索
const handleSearch = () => {
  loadUsers();
};

// 处理筛选
const handleFilter = () => {
  loadUsers();
};

// 刷新列表
const refreshList = () => {
  loadUsers();
};

// 加载用户列表
const loadUsers = async () => {
  const params = {};

  if (searchKeyword.value) {
    params.keyword = searchKeyword.value;
  }

  if (roleFilter.value) {
    params.role = roleFilter.value;
  }

  if (statusFilter.value) {
    params.status = statusFilter.value;
  }

  await getUsers(params);
};

// 显示创建对话框
const showCreateDialog = () => {
  selectedUser.value = null;
  showUserDialog.value = true;
};

// 编辑用户
const editUser = (user) => {
  selectedUser.value = { ...user };
  showUserDialog.value = true;
};

// 重置密码
const resetPassword = (user) => {
  selectedUser.value = user;
  showPasswordDialog.value = true;
};

// 封禁用户
const banUser = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要封禁用户 ${user.username} 吗？`,
      "确认封禁",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      },
    );

    await banUserAPI(user.id);
    await loadUsers();
  } catch (error) {
    if (error !== "cancel") {
      if (import.meta.env.DEV) {
        console.error("封禁用户失败:", error);
      }
    }
  }
};

// 解封用户
const unbanUser = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要解封用户 ${user.username} 吗？`,
      "确认解封",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "info",
      },
    );

    await unbanUserAPI(user.id);
    await loadUsers();
  } catch (error) {
    if (error !== "cancel") {
      if (import.meta.env.DEV) {
        console.error("解封用户失败:", error);
      }
    }
  }
};

// 删除用户
const deleteUser = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 ${user.username} 吗？此操作不可恢复！`,
      "确认删除",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      },
    );

    await deleteUserAPI(user.id);
    await loadUsers();
  } catch (error) {
    if (error !== "cancel") {
      if (import.meta.env.DEV) {
        console.error("删除用户失败:", error);
      }
    }
  }
};

// 处理选择变化
const handleSelectionChange = (selection) => {
  selectedUsers.value = selection;
};

// 处理页面大小变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  loadUsers();
};

// 处理当前页变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  loadUsers();
};

// 处理用户保存
const handleUserSaved = () => {
  loadUsers();
};

// 处理密码重置
const handlePasswordReset = () => {
  ElMessage.success("密码重置成功");
};

// 组件挂载时加载数据
onMounted(() => {
  loadUsers();
});
</script>

<style scoped>
.user-manage-page {
  min-height: 100%;
  background: #f5f5f5;
}

.page-header {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px;
}

.page-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.header-right {
  display: flex;
  gap: 12px;
}

.filter-section {
  padding: 24px;
}

.filter-form {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.page-content {
  padding: 0 24px 24px;
}

.list-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.pagination-container {
  padding: 16px 0;
  display: flex;
  justify-content: center;
  border-top: 1px solid #e4e7ed;
  margin-top: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .header-right {
    width: 100%;
    justify-content: flex-end;
  }

  .filter-form {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-form > * {
    width: 100%;
  }

  .page-content {
    padding: 0 16px 16px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
