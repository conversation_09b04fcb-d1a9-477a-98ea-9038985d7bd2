/**
 * 认证状态管理 Composable
 * 使用 Vue 3 Composition API 管理用户认证状态
 */

import { ref, computed, watch } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { authAPI } from "@/api/auth";
import { clearContractsState } from "./useContracts";
import { clearAllTabsData } from "./useTabs";
import { clearDashboardData } from "./useDashboard";
// 缓存相关导入已移除，系统直接从API获取数据

// 全局状态
const user = ref(null);
const token = ref(localStorage.getItem("token"));
const loading = ref(false);

// 计算属性
const isAuthenticated = computed(() => !!token.value && !!user.value);
const userRole = computed(() => user.value?.role || null);
const userName = computed(() => user.value?.username || "");

// 监听 token 变化，同步到 localStorage
watch(
  token,
  (newToken) => {
    if (newToken) {
      localStorage.setItem("token", newToken);
    } else {
      localStorage.removeItem("token");
    }
  },
  { immediate: true },
);

// 监听 user 变化，同步到 localStorage
watch(
  user,
  (newUser) => {
    if (newUser) {
      localStorage.setItem("user", JSON.stringify(newUser));
    } else {
      localStorage.removeItem("user");
    }
  },
  { deep: true },
);

/**
 * 认证相关方法
 */
export function useAuth() {
  const router = useRouter();

  /**
   * 初始化认证状态
   * 从 localStorage 恢复用户信息
   */
  const initAuth = async () => {
    try {
      const savedToken = localStorage.getItem("token");
      const savedUser = localStorage.getItem("user");

      if (savedToken && savedUser) {
        token.value = savedToken;
        user.value = JSON.parse(savedUser);

        // 验证令牌是否仍然有效
        await verifyToken();
      }
    } catch (error) {
      console.warn("认证初始化失败:", error);
      clearAuth();
    }
  };

  /**
   * 用户登录
   * @param {Object} credentials - 登录凭据
   * @param {string} credentials.username - 用户名
   * @param {string} credentials.password - 密码
   * @returns {Promise<boolean>} 登录是否成功
   */
  const login = async (credentials) => {
    try {
      loading.value = true;

      const response = await authAPI.login(credentials);

      if (response.success) {
        const oldUser = { ...user.value };

        token.value = response.data.token;
        user.value = response.data.user;

        // 清理应用状态
        await performCompleteAuthCleanup(oldUser, user.value);

        ElMessage.success(response.message || "登录成功");

        // 跳转到目标页面或默认页面
        const redirect =
          router.currentRoute.value.query.redirect || "/dashboard";
        await router.push(redirect);

        return true;
      }

      return false;
    } catch (error) {
      // 全局错误处理器会处理API错误，这里只需要返回失败状态
      return false;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 用户登出
   * @param {boolean} showMessage - 是否显示登出消息
   */
  const logout = async (showMessage = true) => {
    try {
      loading.value = true;

      const currentUser = { ...user.value };

      // 调用后端登出接口
      if (token.value) {
        await authAPI.logout();
      }

      // 缓存事件发射代码已移除

      // 执行完整的认证清理
      await performCompleteAuthCleanup(currentUser, null);

      if (showMessage) {
        ElMessage.success("已安全退出");
      }
    } catch (error) {
      console.warn("登出请求失败:", error);
      // 即使后端请求失败，也要清除本地状态
    } finally {
      clearAuth();
      loading.value = false;

      // 跳转到登录页
      await router.push("/login");
    }
  };

  /**
   * 执行完整的认证清理（新版本）
   * @param {Object} oldUser - 旧用户信息
   * @param {Object} newUser - 新用户信息
   */
  const performCompleteAuthCleanup = async (oldUser, newUser) => {
    try {
      // 1. 清理localStorage缓存
      await clearLegacyLocalStorageCache();

      // 2. 清理全局状态
      await clearGlobalApplicationState();

      // 3. 如果是用户切换，确保完全隔离
      if (oldUser && newUser && oldUser.id !== newUser.id) {
        // 额外的用户切换清理逻辑
        await performUserSwitchCleanup(oldUser, newUser);
      }
    } catch (error) {
      console.error("❌ 完整认证清理失败:", error);
    }
  };

  /**
   * 清理传统localStorage缓存
   */
  const clearLegacyLocalStorageCache = async () => {
    try {
      const keysToRemove = [];

      // 遍历localStorage，找到所有缓存相关的键
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (
          key &&
          (key.startsWith("contract-") ||
            key.startsWith("user-") ||
            key.startsWith("dashboard-") ||
            key.startsWith("cache-") ||
            key.startsWith("app-") ||
            key.startsWith("stats-") ||
            key.startsWith("activities-") ||
            key.startsWith("quick-actions-") ||
            key.startsWith("search-") ||
            key.startsWith("tabs-") ||
            key.startsWith("settings-"))
        ) {
          keysToRemove.push(key);
        }
      }

      // 删除找到的缓存键
      keysToRemove.forEach((key) => {
        localStorage.removeItem(key);
      });
    } catch (error) {
      console.error("清理传统localStorage缓存失败:", error);
    }
  };

  /**
   * 清理全局应用状态
   */
  const clearGlobalApplicationState = async () => {
    try {
      // 清除合同相关的全局状态
      if (typeof clearContractsState === "function") {
        clearContractsState();
      }

      // 清除tabs相关数据
      if (typeof clearAllTabsData === "function") {
        clearAllTabsData();
      }

      // 清除dashboard相关数据
      if (typeof clearDashboardData === "function") {
        clearDashboardData();
      }
    } catch (error) {
      console.error("清理全局应用状态失败:", error);
    }
  };

  /**
   * 执行用户切换清理
   */
  const performUserSwitchCleanup = async (oldUser, newUser) => {
    try {
      // 如果角色发生变化，进行深度清理
      if (oldUser.role !== newUser.role) {
        // 清理localStorage中的角色相关数据
        await clearLegacyLocalStorageCache();
      }
    } catch (error) {
      console.error("用户切换清理失败:", error);
    }
  };

  /**
   * 清除所有缓存数据（兼容旧版本）
   * @deprecated 使用 performCompleteAuthCleanup 替代
   */
  const clearAllCaches = () => {
    try {
      // 清除localStorage中的所有缓存数据
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (
          key &&
          (key.startsWith("contract-") ||
            key.startsWith("user-") ||
            key.startsWith("dashboard-") ||
            key.startsWith("cache-") ||
            key.startsWith("app-") ||
            key.startsWith("stats-") ||
            key.startsWith("activities-") ||
            key.startsWith("quick-actions-"))
        ) {
          keysToRemove.push(key);
        }

        // 清理新格式的缓存键
        if (key && key.includes(":")) {
          const patterns = [
            /^tabs:u\d+:r\w+:/,           // tabs:u{userId}:r{role}:*
            /^user:\d+:/,                 // user:{userId}:*
            /^contract:\d+:/,             // contract:{userId}:*
            /^search:\d+:/,               // search:{userId}:*
            /^stats:\d+:/,                // stats:{userId}:*
            /^settings:\d+:/,             // settings:{userId}:*
          ];

          if (patterns.some(pattern => pattern.test(key))) {
            keysToRemove.push(key);
          }
        }
      }

      // 删除找到的缓存键
      keysToRemove.forEach((key) => {
        localStorage.removeItem(key);
        console.log(`🔄 useAuth清理缓存项: ${key}`);
      });

      // 清除合同相关的全局状态
      try {
        clearContractsState();
      } catch (error) {
        console.warn("清除合同状态失败:", error);
      }

      // 清除tabs相关数据
      try {
        clearAllTabsData();
      } catch (error) {
        console.warn("清除tabs数据失败:", error);
      }

      // 清除dashboard相关数据
      try {
        clearDashboardData();
      } catch (error) {
        console.warn("清除dashboard数据失败:", error);
      }
    } catch (error) {
      console.warn("❌ 清除缓存数据失败:", error);
    }
  };

  /**
   * 清除认证状态（新版本）
   */
  const clearAuth = () => {
    // 立即清理内存状态，避免竞态条件
    token.value = null;
    user.value = null;

    // 立即清除localStorage中的关键认证数据
    localStorage.removeItem("token");
    localStorage.removeItem("user");
    localStorage.removeItem("userInfo"); // 重要：防止用户信息残留

    // 缓存管理器调用已移除

    // 清除其他localStorage数据
    localStorage.removeItem("app-settings");
    localStorage.removeItem("sidebar-collapsed");
    localStorage.removeItem("contract-review-tabs");
    localStorage.removeItem("contract-review-active-tab");
  };

  /**
   * 验证令牌有效性
   * @returns {Promise<boolean>} 令牌是否有效
   */
  const verifyToken = async () => {
    try {
      if (!token.value) {
        return false;
      }

      const response = await authAPI.verifyToken();

      if (response.success) {
        // 更新用户信息
        user.value = response.data.user;
        return true;
      }

      return false;
    } catch (error) {
      console.warn("令牌验证失败:", error);
      clearAuth();
      return false;
    }
  };

  /**
   * 刷新用户信息
   */
  const refreshUserInfo = async () => {
    try {
      if (!token.value) {
        return false;
      }

      const response = await authAPI.getProfile();

      if (response.success) {
        user.value = response.data;
        return true;
      }

      return false;
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error("刷新用户信息失败:", error);
      }
      return false;
    }
  };

  /**
   * 修改密码
   * @param {Object} passwordData - 密码数据
   * @returns {Promise<boolean>} 修改是否成功
   */
  const changePassword = async (passwordData) => {
    try {
      loading.value = true;

      const response = await authAPI.changePassword(passwordData);

      if (response.success) {
        ElMessage.success(response.message || "密码修改成功");
        return true;
      }

      return false;
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error("修改密码失败:", error);
      }
      ElMessage.error(error.message || "修改密码失败");
      return false;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 检查用户权限
   * @param {string|Array} roles - 需要的角色
   * @returns {boolean} 是否有权限
   */
  const hasRole = (roles) => {
    if (!user.value) {
      return false;
    }

    if (typeof roles === "string") {
      return user.value.role === roles;
    }

    if (Array.isArray(roles)) {
      return roles.includes(user.value.role);
    }

    return false;
  };

  /**
   * 检查是否为管理员
   * @returns {boolean} 是否为管理员
   */
  const isAdmin = computed(() => hasRole("admin"));

  /**
   * 检查是否为审核员
   * @returns {boolean} 是否为审核员
   */
  const isReviewer = computed(() =>
    hasRole(["county_reviewer", "city_reviewer"]),
  );

  /**
   * 检查是否为员工
   * @returns {boolean} 是否为员工
   */
  const isEmployee = computed(() => hasRole("employee"));

  /**
   * 获取用户显示名称
   * @returns {string} 用户显示名称
   */
  const getDisplayName = () => {
    if (!user.value) {
      return "未登录";
    }

    return user.value.username || "用户";
  };

  /**
   * 获取用户角色显示名称
   * @returns {string} 角色显示名称
   */
  const getRoleDisplayName = () => {
    if (!user.value) {
      return "";
    }

    const roleMap = {
      admin: "管理员",
      county_reviewer: "县级审核员",
      city_reviewer: "市级审核员",
      employee: "员工",
      legal_officer: "法规员",
    };

    return roleMap[user.value.role] || user.value.role;
  };

  return {
    // 状态
    user: computed(() => user.value),
    token: computed(() => token.value),
    loading: computed(() => loading.value),
    isAuthenticated,
    userRole,
    userName,
    isAdmin,
    isReviewer,
    isEmployee,

    // 方法
    initAuth,
    login,
    logout,
    clearAuth,
    verifyToken,
    refreshUserInfo,
    changePassword,
    hasRole,
    getDisplayName,
    getRoleDisplayName,
  };
}
