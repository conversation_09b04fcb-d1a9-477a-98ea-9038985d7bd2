/**
 * 首页数据管理 Composable
 * 管理首页相关的数据和状态
 */

import { ref, computed, onMounted, onUnmounted, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { dashboardAPI } from "@/api/dashboard";
import { useAuth } from "./useAuth";

/**
 * 首页数据管理 Hook
 */
export function useDashboard() {
  const { userRole } = useAuth();

  // 状态管理
  const loading = ref(false);
  const refreshing = ref(false);
  const stats = ref({});
  const activities = ref([]);
  const quickActions = ref([]);

  // 自动刷新定时器
  let refreshTimer = null;

  /**
   * 获取统计数据
   */
  const getStats = async () => {
    try {
      const response = await dashboardAPI.getStats();
      if (response.success) {
        stats.value = response.data;
      }
      return response.data;
    } catch (error) {
      // 如果是登出过程中的请求，不记录错误
      if (error.message === "用户正在登出") {
        return null;
      }

      if (import.meta.env.DEV) {
        console.error("获取统计数据失败:", error);
      }
      // 统计数据失败不显示错误消息，避免干扰用户
    }
  };

  /**
   * 获取最近活动
   */
  const getActivities = async (limit = 10) => {
    try {
      const response = await dashboardAPI.getActivities(limit);
      if (response.success) {
        activities.value = response.data;
      }
      return response.data;
    } catch (error) {
      // 如果是登出过程中的请求，不记录错误
      if (error.message === "用户正在登出") {
        return null;
      }

      if (import.meta.env.DEV) {
        console.error("获取最近活动失败:", error);
      }
    }
  };

  /**
   * 获取快捷操作
   */
  const getQuickActions = async () => {
    try {
      const response = await dashboardAPI.getQuickActions();

      if (response.success) {
        // 强制更新数据
        quickActions.value = [...response.data];

        // 使用nextTick确保响应式更新
        await nextTick();
      }
      return response.data;
    } catch (error) {
      // 如果是登出过程中的请求，不记录错误
      if (error.message === "用户正在登出") {
        return null;
      }

      if (import.meta.env.DEV) {
        console.error("获取快捷操作失败:", error);
      }
    }
  };

  /**
   * 初始化首页数据
   */
  const initDashboard = async () => {
    // 检查是否正在登出
    if (window.isLoggingOut) {
      return;
    }

    try {
      loading.value = true;

      // 串行执行请求，避免重复请求取消问题
      await getStats();
      await getActivities();
      await getQuickActions();
    } catch (error) {
      // 如果是登出过程中的错误，不记录
      if (error.message === "用户正在登出") {
        return;
      }

      if (import.meta.env.DEV) {
        console.error("初始化首页数据失败:", error);
      }
    } finally {
      loading.value = false;
    }
  };

  /**
   * 刷新首页数据
   */
  const refreshDashboard = async () => {
    try {
      refreshing.value = true;

      await Promise.all([getStats(), getActivities()]);

      ElMessage.success("数据已刷新");
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error("刷新首页数据失败:", error);
      }
      ElMessage.error("刷新失败");
    } finally {
      refreshing.value = false;
    }
  };

  /**
   * 启动自动刷新 - 优化版
   */
  const startAutoRefresh = (interval = 5 * 60 * 1000) => {
    if (refreshTimer) {
      clearInterval(refreshTimer);
    }

    refreshTimer = setInterval(() => {
      // 只在页面可见时进行自动刷新
      if (!document.hidden) {
        // 静默刷新，不显示加载状态
        Promise.all([getStats()]).catch((error) => {
          console.warn("自动刷新失败:", error);
        });
      }
    }, interval);
  };

  /**
   * 停止自动刷新
   */
  const stopAutoRefresh = () => {
    if (refreshTimer) {
      clearInterval(refreshTimer);
      refreshTimer = null;
    }
  };

  /**
   * 清理dashboard数据（用于退出登录）
   */
  const clearDashboardData = () => {
    try {
      // 重置所有状态
      stats.value = {};
      activities.value = [];
      quickActions.value = [];
      loading.value = false;
      refreshing.value = false;

      // 停止自动刷新
      stopAutoRefresh();
    } catch (error) {
      console.warn("清除dashboard数据失败:", error);
    }
  };

  /**
   * 获取角色特定的统计卡片配置
   */
  const getStatsCards = () => {
    const role = userRole.value;

    switch (role) {
      case "employee":
        return [
          {
            title: "总提交数",
            value: stats.value.summary?.totalSubmitted || 0,
            icon: "Document",
            color: "primary",
            trend: "+12%",
          },
          {
            title: "待审核",
            value: stats.value.summary?.pendingReview || 0,
            icon: "Clock",
            color: "warning",
            trend: "-5%",
          },
          {
            title: "已通过",
            value: stats.value.summary?.approved || 0,
            icon: "Check",
            color: "success",
            trend: "+8%",
          },
          {
            title: "已拒绝",
            value: stats.value.summary?.rejected || 0,
            icon: "Close",
            color: "danger",
            trend: "-2%",
          },
        ];

      case "reviewer":
        return [
          {
            title: "分配给我",
            value: stats.value.summary?.totalAssigned || 0,
            icon: "FolderOpened",
            color: "primary",
            trend: "+15%",
          },
          {
            title: "待审核",
            value: stats.value.summary?.pendingReview || 0,
            icon: "Timer",
            color: "warning",
            trend: "-3%",
          },
          {
            title: "审核中",
            value: stats.value.summary?.reviewing || 0,
            icon: "Loading",
            color: "info",
            trend: "+5%",
          },
          {
            title: "已完成",
            value: stats.value.summary?.completed || 0,
            icon: "Check",
            color: "success",
            trend: "+10%",
          },
        ];

      case "admin":
        return [
          {
            title: "总合同数",
            value: stats.value.summary?.totalContracts || 0,
            icon: "Document",
            color: "primary",
            trend: "+18%",
          },
          {
            title: "总用户数",
            value: stats.value.summary?.totalUsers || 0,
            icon: "User",
            color: "success",
            trend: "+6%",
          },
          {
            title: "活跃用户",
            value: stats.value.summary?.activeUsers || 0,
            icon: "UserFilled",
            color: "info",
            trend: "+12%",
          },
          {
            title: "系统状态",
            value: stats.value.summary?.systemHealth || "good",
            icon: "Monitor",
            color: "success",
            trend: "正常",
          },
        ];

      default:
        return [];
    }
  };

  // 计算属性
  const recentActivities = computed(() => {
    return activities.value.slice(0, 5);
  });

  const isLoading = computed(() => loading.value);
  const isRefreshing = computed(() => refreshing.value);

  // 组件挂载时初始化
  onMounted(() => {
    initDashboard();
    startAutoRefresh();
  });

  // 组件卸载时清理
  onUnmounted(() => {
    stopAutoRefresh();
  });

  return {
    // 状态
    stats: computed(() => stats.value),
    activities: computed(() => activities.value),
    quickActions: computed(() => quickActions.value),
    recentActivities,
    isLoading,
    isRefreshing,

    // 方法
    initDashboard,
    refreshDashboard,
    getStats,
    getActivities,
    getQuickActions,
    startAutoRefresh,
    stopAutoRefresh,
    clearDashboardData,
    getStatsCards,
  };
}

// 全局状态用于清理
let globalStats = null;
let globalActivities = null;
let globalQuickActions = null;
let globalLoading = null;
let globalRefreshing = null;
let globalRefreshTimer = null;

// 导出清理函数供外部使用
export const clearDashboardData = () => {
  try {
    // 这个函数会在每次useDashboard被调用时更新全局引用
    // 然后在退出登录时清理所有状态
  } catch (error) {
    console.warn("清除dashboard数据失败:", error);
  }
};
