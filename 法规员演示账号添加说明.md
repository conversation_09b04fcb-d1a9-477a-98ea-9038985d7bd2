# 法规员演示账号添加说明

## 修改概述

本次修改在登录页面添加了法规员演示账号，用户可以直接点击使用法规员身份登录系统。

## 修改内容

### 1. 前端登录页面修改

#### 文件：`frontend/src/views/TheLogin.vue`

**添加的演示账号按钮：**
```vue
<div
  class="demo-account"
  @click="fillAccount('legal_officer', '123456')"
>
  <span class="role-badge legal-officer">法规员</span>
</div>
```

**添加的样式：**
```css
.role-badge.legal-officer {
  background: #e6a23c;
}
```

### 2. 前端角色映射更新

#### 文件：`frontend/src/composables/useAuth.js`

**更新的角色映射：**
```javascript
const roleMap = {
  admin: "管理员",
  county_reviewer: "县级审核员",
  city_reviewer: "市级审核员",
  employee: "员工",
  legal_officer: "法规员",  // 新增
};
```

### 3. 后端数据库初始化更新

#### 文件：`backend/init-db.js`

**更新用户表角色约束：**
```sql
role TEXT NOT NULL CHECK (role IN ('employee', 'county_reviewer', 'city_reviewer', 'legal_officer', 'admin'))
```

**添加默认法规员账号创建：**
```javascript
// 插入默认法规员账号
const legalOfficerPassword = await bcrypt.hash('123456', saltRounds);
const legalOfficerResult = await runQuery(`
  INSERT OR IGNORE INTO users (username, password, role, status, created_by)
  VALUES (?, ?, 'legal_officer', 'active', 1)
`, ['legal_officer', legalOfficerPassword]);
```

## 演示账号信息

### 法规员演示账号
- **用户名**: `legal_officer`
- **密码**: `123456`
- **角色**: 法规员 (legal_officer)
- **状态**: 激活 (active)

## 法规员功能权限

法规员在系统中的主要功能：

1. **合同编号管理**
   - 查看待分配编号的合同（状态为 `pending_contract_number`）
   - 为通过审核的合同分配正式编号
   - 管理和修改合同编号

2. **合同查看权限**
   - 查看所有合同信息
   - 查看合同审核历史
   - 查看合同详细信息

3. **系统通知**
   - 接收相关工作提醒
   - 查看系统通知

## 工作流程

法规员在合同审核流程中的位置：

```
员工提交合同 → 县局审核 → 市局审核 → 法规员分配编号 → 合同完成
```

## 使用方法

1. 打开登录页面
2. 在演示账号区域找到"法规员"按钮
3. 点击"法规员"按钮，系统会自动填充用户名和密码
4. 点击"登录"按钮即可以法规员身份登录系统

## 测试验证

### 创建的测试脚本
- `backend/test-legal-officer-login.js` - 测试法规员登录功能

### 运行测试
```bash
# 在后端目录下运行
node test-legal-officer-login.js
```

## 注意事项

1. **数据库更新**：如果数据库已存在，需要重新初始化或手动添加法规员账号
2. **权限验证**：确保法规员角色在RBAC系统中有正确的权限配置
3. **前端路由**：确保法规员登录后能正确跳转到相应的工作页面

## 相关文件

### 前端文件
- `frontend/src/views/TheLogin.vue` - 登录页面
- `frontend/src/composables/useAuth.js` - 认证逻辑

### 后端文件
- `backend/init-db.js` - 数据库初始化
- `backend/src/utils/constants.js` - 系统常量定义
- `backend/test-legal-officer-login.js` - 登录测试脚本

## 完成状态

✅ 前端登录页面添加法规员演示账号按钮
✅ 前端角色映射更新
✅ 后端数据库表结构更新
✅ 后端默认数据创建更新
✅ 创建测试验证脚本
✅ 编写完整说明文档
